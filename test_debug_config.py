# test_debug_config.py - 测试调试配置功能
import os
import time
import numpy as np
import cv2
from logger_manager import log_info, log_success, log_warning, log_error
from debug_config_manager import (
    get_debug_config_manager, 
    enable_debug_mode, 
    enable_save_intermediate,
    print_debug_status
)

def test_debug_config_manager():
    """测试调试配置管理器"""
    log_info("=== 测试调试配置管理器 ===")
    
    # 获取配置管理器
    manager = get_debug_config_manager()
    
    # 打印当前状态
    manager.print_debug_status()
    
    # 测试启用调试模式
    log_info("启用调试模式...")
    manager.enable_debug_mode(True)
    manager.enable_save_intermediate(True)
    
    # 更新其他配置
    manager.update_debug_config(
        save_original_screenshot=True,
        save_cropped_image=True,
        save_detection_result=True,
        image_format="png",
        add_timestamp_to_filename=True
    )
    
    # 保存配置
    if manager.save_config():
        log_success("配置保存成功")
    else:
        log_error("配置保存失败")
    
    # 打印更新后的状态
    log_info("更新后的配置:")
    manager.print_debug_status()

def test_debug_directory():
    """测试调试目录创建"""
    log_info("=== 测试调试目录创建 ===")
    
    manager = get_debug_config_manager()
    
    # 创建调试目录
    debug_dir = manager.create_debug_directory()
    if debug_dir:
        log_success(f"调试目录创建成功: {debug_dir}")
        
        # 检查目录是否存在
        if os.path.exists(debug_dir):
            log_success("目录确实存在")
        else:
            log_error("目录创建失败")
    else:
        log_error("调试目录创建失败")

def test_debug_image_processing():
    """测试调试模式下的图像处理"""
    log_info("=== 测试调试模式图像处理 ===")
    
    # 启用调试模式
    enable_debug_mode(True)
    enable_save_intermediate(True)
    
    # 创建测试图像
    original_image = np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8)
    cropped_image = original_image[100:400, 200:600]
    
    # 模拟股票信息
    stock_value_dict = {
        'code': 'TEST001',
        'name': '测试股票',
        'img_save_path': 'test_path.png'
    }
    
    # 模拟配置信息
    config = {
        'lines_range': [[100, 200], [300, 400], [500, 600]]
    }
    
    try:
        # 导入gen_signal模块进行测试
        import gen_signal
        
        # 获取调试配置
        debug_manager = get_debug_config_manager()
        debug_config = debug_manager.get_debug_config()
        
        # 调用调试处理函数
        result = gen_signal._process_with_debug_images(
            original_image, cropped_image, stock_value_dict, config, debug_config
        )
        
        log_success(f"调试图像处理完成，结果: {result}")
        
        # 检查生成的文件
        debug_dir = debug_manager.get_debug_output_dir()
        if os.path.exists(debug_dir):
            files = os.listdir(debug_dir)
            log_info(f"生成的调试文件: {files}")
            
            # 统计文件类型
            png_files = [f for f in files if f.endswith('.png')]
            txt_files = [f for f in files if f.endswith('.txt')]
            
            log_info(f"PNG文件数量: {len(png_files)}")
            log_info(f"TXT文件数量: {len(txt_files)}")
            
        else:
            log_warning("调试目录不存在")
            
    except Exception as e:
        log_error(f"调试图像处理测试失败: {e}")

def test_config_switching():
    """测试配置切换"""
    log_info("=== 测试配置切换 ===")
    
    manager = get_debug_config_manager()
    
    # 测试不同的配置组合
    test_configs = [
        {"debug": False, "save": False, "description": "生产模式"},
        {"debug": True, "save": False, "description": "调试模式但不保存图像"},
        {"debug": True, "save": True, "description": "完整调试模式"},
        {"debug": False, "save": True, "description": "无效组合（调试关闭但保存开启）"}
    ]
    
    for config in test_configs:
        log_info(f"测试配置: {config['description']}")
        
        manager.enable_debug_mode(config["debug"])
        manager.enable_save_intermediate(config["save"])
        
        # 检查状态
        debug_enabled = manager.is_debug_enabled()
        save_enabled = manager.is_save_intermediate_enabled()
        
        log_info(f"  调试模式: {debug_enabled}")
        log_info(f"  保存中间图像: {save_enabled}")
        
        # 验证逻辑
        if config["debug"] and config["save"]:
            expected_behavior = "保存调试图像"
        else:
            expected_behavior = "内存处理"
        
        log_info(f"  预期行为: {expected_behavior}")
        log_info("---")

def test_cleanup_functionality():
    """测试清理功能"""
    log_info("=== 测试清理功能 ===")
    
    manager = get_debug_config_manager()
    debug_dir = manager.get_debug_output_dir()
    
    # 确保调试目录存在
    os.makedirs(debug_dir, exist_ok=True)
    
    # 创建一些测试文件
    test_files = []
    for i in range(15):
        filename = f"test_file_{i:02d}.png"
        filepath = os.path.join(debug_dir, filename)
        
        # 创建空文件
        with open(filepath, 'w') as f:
            f.write(f"test file {i}")
        
        test_files.append(filepath)
        time.sleep(0.01)  # 确保文件有不同的修改时间
    
    log_info(f"创建了 {len(test_files)} 个测试文件")
    
    # 执行清理，保留最新的5个文件
    manager.cleanup_debug_directory(keep_latest=5)
    
    # 检查剩余文件
    remaining_files = os.listdir(debug_dir)
    log_info(f"清理后剩余 {len(remaining_files)} 个文件")
    
    if len(remaining_files) <= 5:
        log_success("清理功能正常工作")
    else:
        log_warning(f"清理后文件数量超出预期: {len(remaining_files)}")
    
    # 清理所有测试文件
    for filename in remaining_files:
        filepath = os.path.join(debug_dir, filename)
        try:
            os.remove(filepath)
        except:
            pass

def test_configuration_persistence():
    """测试配置持久化"""
    log_info("=== 测试配置持久化 ===")
    
    # 创建第一个管理器实例并设置配置
    manager1 = get_debug_config_manager()
    manager1.enable_debug_mode(True)
    manager1.enable_save_intermediate(True)
    manager1.update_debug_config(image_format="jpg")
    manager1.save_config()
    
    # 创建第二个管理器实例，应该加载相同的配置
    from debug_config_manager import DebugConfigManager
    manager2 = DebugConfigManager()
    
    # 验证配置是否一致
    if (manager2.is_debug_enabled() == manager1.is_debug_enabled() and
        manager2.is_save_intermediate_enabled() == manager1.is_save_intermediate_enabled()):
        log_success("配置持久化正常工作")
    else:
        log_error("配置持久化失败")
    
    log_info(f"管理器1 - 调试模式: {manager1.is_debug_enabled()}")
    log_info(f"管理器2 - 调试模式: {manager2.is_debug_enabled()}")

def main():
    """主测试函数"""
    log_info("开始调试配置功能测试")
    log_info("=" * 60)
    
    test_debug_config_manager()
    test_debug_directory()
    test_config_switching()
    test_cleanup_functionality()
    test_configuration_persistence()
    
    # 最后测试图像处理（可能需要更多依赖）
    try:
        test_debug_image_processing()
    except ImportError as e:
        log_warning(f"跳过图像处理测试，缺少依赖: {e}")
    except Exception as e:
        log_error(f"图像处理测试失败: {e}")
    
    log_success("调试配置功能测试完成")
    log_info("=" * 60)
    
    print("\n使用说明:")
    print("1. 启动Web控制台: python app.py")
    print("2. 在浏览器中访问调试配置面板")
    print("3. 启用调试模式和保存中间图像")
    print("4. 运行任务查看调试输出")
    print("5. 检查 resources/cache/debug 目录中的调试文件")

if __name__ == "__main__":
    main()
