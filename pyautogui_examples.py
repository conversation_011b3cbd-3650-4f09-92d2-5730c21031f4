"""
PyAutoGUI模板匹配功能使用示例

这个文件展示如何使用PyAutoGUI进行图像模板匹配和自动化操作
"""

from open_ths_pyauto import *
import time

def example_1_basic_template_search():
    """
    示例1: 基本的模板搜索
    """
    print("=== 示例1: 基本模板搜索 ===")
    
    # 在整个屏幕上查找模板
    template_path = "search_box_template.png"
    
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        print("请先创建模板文件")
        return
    
    print(f"在屏幕上查找模板: {template_path}")
    
    # 查找模板
    result = locate_template_on_screen(template_path, confidence=0.8)
    
    if result['found']:
        print(f"✓ 找到模板!")
        print(f"  位置: {result['center']}")
        print(f"  区域: {result['location']}")
    else:
        print("✗ 未找到模板")

def example_2_click_template():
    """
    示例2: 查找并点击模板
    """
    print("\n=== 示例2: 查找并点击模板 ===")
    
    template_path = "button_template.png"
    
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        print("请先创建按钮模板文件")
        return
    
    print(f"查找并点击模板: {template_path}")
    
    # 查找并点击
    success = click_template_on_screen(template_path, confidence=0.8)
    
    if success:
        print("✓ 成功点击模板位置!")
    else:
        print("✗ 未找到模板或点击失败")

def example_3_wait_for_element():
    """
    示例3: 等待界面元素出现
    """
    print("\n=== 示例3: 等待界面元素出现 ===")
    
    template_path = "loading_complete_template.png"
    
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        print("这个示例需要一个表示加载完成的模板")
        return
    
    print(f"等待模板出现: {template_path}")
    print("最多等待15秒...")
    
    # 等待模板出现
    result = wait_for_template_on_screen(template_path, timeout=15, confidence=0.8)
    
    if result['found']:
        print("✓ 界面元素已出现!")
        print(f"  位置: {result['center']}")
    else:
        print("✗ 等待超时，界面元素未出现")

def example_4_window_region_search():
    """
    示例4: 在窗口区域内搜索
    """
    print("\n=== 示例4: 窗口区域搜索 ===")
    
    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        print("无法启动同花顺")
        return
    
    main_window = connect_to_window(process)
    if not main_window:
        print("无法连接到窗口")
        return
    
    template_path = "search_box_template.png"
    
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return
    
    print("在同花顺窗口区域内搜索模板...")
    
    # 在窗口区域搜索
    result = locate_template_in_window(main_window, template_path, confidence=0.8)
    
    if result['found']:
        print("✓ 在窗口中找到模板!")
        print(f"  位置: {result['center']}")
        
        # 点击找到的位置
        success = click_template_in_window(main_window, template_path, confidence=0.8)
        if success:
            print("✓ 成功点击模板位置!")
            
            # 在点击后输入内容
            time.sleep(0.5)
            pyautogui.typewrite("000001")  # 输入股票代码
            pyautogui.press("enter")       # 按回车
            print("✓ 已输入股票代码并回车")
        else:
            print("✗ 点击失败")
    else:
        print("✗ 在窗口中未找到模板")

def example_5_multiple_templates():
    """
    示例5: 查找多个模板
    """
    print("\n=== 示例5: 查找多个模板 ===")
    
    # 定义多个模板
    templates = [
        ("search_box_template.png", "搜索框"),
        ("buy_button_template.png", "买入按钮"),
        ("sell_button_template.png", "卖出按钮"),
        ("menu_template.png", "菜单")
    ]
    
    found_templates = []
    
    print("查找多个模板...")
    
    for template_path, template_name in templates:
        if os.path.exists(template_path):
            print(f"查找 {template_name}...")
            result = locate_template_on_screen(template_path, confidence=0.8)
            
            if result['found']:
                found_templates.append((template_name, result))
                print(f"  ✓ 找到 {template_name} 在 {result['center']}")
            else:
                print(f"  ✗ 未找到 {template_name}")
        else:
            print(f"  - {template_name} 模板文件不存在")
    
    print(f"\n总共找到 {len(found_templates)} 个模板")
    
    # 可以根据需要点击特定模板
    if found_templates:
        print("可以点击的模板:")
        for i, (name, result) in enumerate(found_templates):
            print(f"  {i+1}. {name} - 位置: {result['center']}")

def example_6_auto_trading_workflow():
    """
    示例6: 自动交易工作流程
    """
    print("\n=== 示例6: 自动交易工作流程 ===")
    
    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        return
    
    main_window = connect_to_window(process)
    if not main_window:
        return
    
    # 定义工作流程中需要的模板
    workflow_templates = {
        "search_box": "search_box_template.png",
        "buy_button": "buy_button_template.png",
        "quantity_input": "quantity_input_template.png",
        "confirm_button": "confirm_button_template.png"
    }
    
    # 检查所有模板是否存在
    missing_templates = []
    for name, path in workflow_templates.items():
        if not os.path.exists(path):
            missing_templates.append(f"{name} ({path})")
    
    if missing_templates:
        print("缺少以下模板文件:")
        for template in missing_templates:
            print(f"  - {template}")
        print("请先创建这些模板文件")
        return
    
    print("开始自动交易工作流程...")
    
    try:
        # 步骤1: 搜索股票
        print("步骤1: 搜索股票...")
        if click_template_in_window(main_window, workflow_templates["search_box"]):
            time.sleep(0.5)
            pyautogui.typewrite("000001")  # 平安银行
            pyautogui.press("enter")
            print("✓ 已搜索股票")
            time.sleep(2)  # 等待搜索结果
        else:
            print("✗ 未找到搜索框")
            return
        
        # 步骤2: 点击买入按钮
        print("步骤2: 点击买入按钮...")
        if click_template_in_window(main_window, workflow_templates["buy_button"]):
            print("✓ 已点击买入按钮")
            time.sleep(1)  # 等待买入界面加载
        else:
            print("✗ 未找到买入按钮")
            return
        
        # 步骤3: 输入数量
        print("步骤3: 输入购买数量...")
        if click_template_in_window(main_window, workflow_templates["quantity_input"]):
            time.sleep(0.5)
            pyautogui.typewrite("100")  # 购买100股
            print("✓ 已输入购买数量")
        else:
            print("✗ 未找到数量输入框")
            return
        
        # 步骤4: 确认交易（注意：这里只是演示，实际使用时要谨慎）
        print("步骤4: 确认交易...")
        print("⚠️  注意: 这是演示模式，不会实际执行交易")
        # if click_template_in_window(main_window, workflow_templates["confirm_button"]):
        #     print("✓ 交易已确认")
        # else:
        #     print("✗ 未找到确认按钮")
        
        print("✓ 自动交易工作流程演示完成")
        
    except Exception as e:
        print(f"✗ 工作流程执行失败: {str(e)}")

def example_7_error_handling():
    """
    示例7: 错误处理和重试机制
    """
    print("\n=== 示例7: 错误处理和重试 ===")
    
    template_path = "unstable_template.png"
    max_retries = 3
    retry_interval = 1
    
    print(f"尝试查找模板: {template_path}")
    print(f"最大重试次数: {max_retries}")
    
    for attempt in range(max_retries):
        print(f"尝试 {attempt + 1}/{max_retries}...")
        
        try:
            result = locate_template_on_screen(template_path, confidence=0.8)
            
            if result['found']:
                print(f"✓ 第 {attempt + 1} 次尝试成功!")
                print(f"  位置: {result['center']}")
                return True
            else:
                print(f"✗ 第 {attempt + 1} 次尝试未找到模板")
                
        except Exception as e:
            print(f"✗ 第 {attempt + 1} 次尝试出错: {str(e)}")
        
        if attempt < max_retries - 1:
            print(f"等待 {retry_interval} 秒后重试...")
            time.sleep(retry_interval)
    
    print("✗ 所有尝试都失败了")
    return False

def run_all_examples():
    """
    运行所有示例
    """
    print("PyAutoGUI模板匹配功能示例")
    print("=" * 40)
    
    examples = [
        example_1_basic_template_search,
        example_2_click_template,
        example_3_wait_for_element,
        example_4_window_region_search,
        example_5_multiple_templates,
        example_6_auto_trading_workflow,
        example_7_error_handling
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
            print(f"\n{'='*20} 示例 {i} 完成 {'='*20}")
            
            if i < len(examples):
                input("按回车键继续下一个示例...")
                
        except KeyboardInterrupt:
            print("\n用户中断了示例执行")
            break
        except Exception as e:
            print(f"\n示例 {i} 执行出错: {str(e)}")
            continue

if __name__ == "__main__":
    print("PyAutoGUI模板匹配使用示例")
    print("=" * 30)
    
    while True:
        print("\n请选择要运行的示例:")
        print("1. 基本模板搜索")
        print("2. 查找并点击模板")
        print("3. 等待界面元素出现")
        print("4. 窗口区域搜索")
        print("5. 查找多个模板")
        print("6. 自动交易工作流程")
        print("7. 错误处理和重试")
        print("8. 运行所有示例")
        print("9. 退出")
        
        choice = input("请输入选择 (1-9): ").strip()
        
        if choice == "1":
            example_1_basic_template_search()
        elif choice == "2":
            example_2_click_template()
        elif choice == "3":
            example_3_wait_for_element()
        elif choice == "4":
            example_4_window_region_search()
        elif choice == "5":
            example_5_multiple_templates()
        elif choice == "6":
            example_6_auto_trading_workflow()
        elif choice == "7":
            example_7_error_handling()
        elif choice == "8":
            run_all_examples()
        elif choice == "9":
            print("退出示例程序")
            break
        else:
            print("无效选择，请重新输入")
