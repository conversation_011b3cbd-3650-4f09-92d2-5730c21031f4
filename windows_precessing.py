import get_screenshot_tool
import lines_detetor
import capture_imge
import json
import tkinter as tk
import pyautogui
import keyboard
import threading
import os
from datetime import datetime

"""
使用这个文件对窗口进行预处理

传统模式：
1. 使用影刀进行截图，保存到"C:/Users/<USER>/Documents/ths_gs_resource/template/precessing_main_widnows.jpg"
2. 调用screenshot_tool来手动指定K线的范围，将p1和p2保存到json文件
3. 调用capture_image中的resize_img_fullimg来截图, 注意截图的时候需要正好截到两边K线停止的地方
4. 调用lines_detetor来识别K线的坐标，将15根K线的坐标保存到json文件

新增交互式模式：
5. 交互式屏幕截图功能 - 通过全局快捷键 Ctrl+Shift+S 触发
6. 全屏区域选择 - 用户拖拽选择截图区域
7. 自动截图保存 - 保存到 resources/images/ 目录
8. K线中心点选择 - 直接在截图上点击K线中心点，无需选择区域
9. 自动配置更新 - 直接更新K线坐标和范围信息
"""

class InteractiveScreenCapture:
    """交互式屏幕截图工具类"""

    def __init__(self):
        self.root = None
        self.canvas = None
        self.start_x = None
        self.start_y = None
        self.end_x = None
        self.end_y = None
        self.rect_id = None
        self.screenshot = None
        self.is_selecting = False
        self.selection_complete = False

    def create_fullscreen_overlay(self):
        """创建全屏半透明覆盖层"""
        self.root = tk.Tk()
        self.root.attributes('-fullscreen', True)
        self.root.attributes('-topmost', True)
        self.root.attributes('-alpha', 0.3)  # 半透明
        self.root.configure(bg='black')
        self.root.overrideredirect(True)  # 去除窗口边框

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 创建画布
        self.canvas = tk.Canvas(
            self.root,
            width=screen_width,
            height=screen_height,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack()

        # 绑定鼠标事件
        self.canvas.bind('<Button-1>', self.on_click)
        self.canvas.bind('<B1-Motion>', self.on_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_release)

        # 绑定键盘事件
        self.root.bind('<Escape>', self.cancel_selection)
        self.root.focus_set()

        # 显示提示信息
        self.show_instructions()

    def show_instructions(self):
        """显示操作说明"""
        instruction_text = "拖拽鼠标选择截图区域\nESC键取消选择"
        self.canvas.create_text(
            self.root.winfo_screenwidth() // 2,
            50,
            text=instruction_text,
            fill='white',
            font=('Arial', 16),
            justify='center'
        )

    def on_click(self, event):
        """鼠标点击事件"""
        if not self.is_selecting:
            self.start_x = event.x
            self.start_y = event.y
            self.is_selecting = True

    def on_drag(self, event):
        """鼠标拖拽事件"""
        if self.is_selecting:
            # 删除之前的矩形
            if self.rect_id:
                self.canvas.delete(self.rect_id)

            # 绘制新的选择矩形
            self.rect_id = self.canvas.create_rectangle(
                self.start_x, self.start_y, event.x, event.y,
                outline='red', width=2, fill='', stipple='gray50'
            )

    def on_release(self, event):
        """鼠标释放事件"""
        if self.is_selecting:
            self.end_x = event.x
            self.end_y = event.y
            self.is_selecting = False
            self.selection_complete = True

            # 确保坐标顺序正确
            x1 = min(self.start_x, self.end_x)
            y1 = min(self.start_y, self.end_y)
            x2 = max(self.start_x, self.end_x)
            y2 = max(self.start_y, self.end_y)

            self.start_x, self.start_y = x1, y1
            self.end_x, self.end_y = x2, y2

            # 显示确认信息
            self.show_confirmation()

    def show_confirmation(self):
        """显示确认信息"""
        width = self.end_x - self.start_x
        height = self.end_y - self.start_y

        confirm_text = f"选择区域: ({self.start_x}, {self.start_y}) 到 ({self.end_x}, {self.end_y})\n"
        confirm_text += f"尺寸: {width} x {height}\n"
        confirm_text += "按回车确认，ESC取消"

        # 清除之前的文本
        self.canvas.delete('instruction')

        # 显示确认文本
        self.canvas.create_text(
            self.root.winfo_screenwidth() // 2,
            100,
            text=confirm_text,
            fill='yellow',
            font=('Arial', 14),
            justify='center',
            tags='confirmation'
        )

        # 绑定确认键
        self.root.bind('<Return>', self.confirm_selection)

    def confirm_selection(self, event=None):
        """确认选择并关闭覆盖层"""
        self.root.destroy()

    def cancel_selection(self, event=None):
        """取消选择"""
        self.start_x = self.start_y = self.end_x = self.end_y = None
        self.selection_complete = False
        self.root.destroy()

    def get_selection_coordinates(self):
        """获取选择的坐标"""
        if self.selection_complete and all(coord is not None for coord in [self.start_x, self.start_y, self.end_x, self.end_y]):
            return [(self.start_x, self.start_y), (self.end_x, self.end_y)]
        return None


def interactive_screenshot_capture():
    """交互式屏幕截图主函数"""
    print("启动交互式屏幕截图功能...")
    print("操作说明：")
    print("1. 在全屏覆盖层上拖拽鼠标选择截图区域")
    print("2. 按回车键确认选择")
    print("3. 按ESC键取消选择")

    # 创建截图工具实例
    capture_tool = InteractiveScreenCapture()

    try:
        # 创建全屏覆盖层
        capture_tool.create_fullscreen_overlay()

        # 启动GUI主循环
        capture_tool.root.mainloop()

        # 获取选择的坐标
        coordinates = capture_tool.get_selection_coordinates()

        if coordinates:
            print(f"选择的区域坐标: {coordinates}")

            # 执行截图
            screenshot_path = capture_selected_region(coordinates)

            if screenshot_path:
                print(f"截图已保存到: {screenshot_path}")

                # 自动进行K线手动选择
                process_kline_selection(screenshot_path, coordinates)

                return screenshot_path
            else:
                print("截图失败")
                return None
        else:
            print("未选择任何区域")
            return None

    except Exception as e:
        print(f"交互式截图过程中发生错误: {e}")
        return None


def capture_selected_region(coordinates):
    """根据选择的坐标截取屏幕区域"""
    try:
        p1, p2 = coordinates
        x1, y1 = p1
        x2, y2 = p2

        # 计算区域尺寸
        width = x2 - x1
        height = y2 - y1

        if width <= 0 or height <= 0:
            print("无效的截图区域尺寸")
            return None

        # 使用pyautogui截图
        screenshot = pyautogui.screenshot(region=(x1, y1, width, height))

        # 生成保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_dir = os.path.join("resources", "images")
        os.makedirs(save_dir, exist_ok=True)

        filename = f"interactive_screenshot_{timestamp}.png"
        save_path = os.path.join(save_dir, filename)

        # 保存截图
        screenshot.save(save_path)

        print(f"区域截图成功: {save_path}")
        print(f"截图区域: ({x1}, {y1}) 到 ({x2}, {y2})")
        print(f"截图尺寸: {width} x {height}")

        return save_path

    except Exception as e:
        print(f"截图过程中发生错误: {e}")
        return None


def process_kline_selection(screenshot_path, original_coordinates):
    """处理K线手动选择"""
    try:
        print("\n开始K线手动选择...")
        print("将直接在截图上进行K线中心点选择...")

        # 直接进行K线中心点选择，跳过区域选择步骤
        # 使用截图作为输入，直接调用K线检测的手动模式
        coors = lines_detetor.detect_and_predict_vertical_lines(screenshot_path, mode='manual')

        if coors and len(coors) > 0:
            # 更新配置文件，使用K线中心点坐标
            update_config_with_kline_centers(coors, screenshot_path, original_coordinates)

            print(f"K线中心点选择完成！检测到 {len(coors)} 条K线")
            print(f"K线x坐标: {coors}")
            return True
        else:
            print("未获取到有效的K线中心点坐标")
            return False

    except Exception as e:
        print(f"K线选择过程中发生错误: {e}")
        return False


def update_config_with_kline_centers(kline_centers, screenshot_path, original_coords):
    """使用K线中心点坐标更新配置文件"""
    try:
        json_path = r"resources/coor_config.json"

        # 读取现有配置
        with open(json_path, 'r', encoding='utf-8') as json_file:
            data = json.load(json_file)

        # 更新主要的K线捕获坐标（用户选择的截图区域）
        data["main_kline_capture_coor"]["p1"] = original_coords[0]
        data["main_kline_capture_coor"]["p2"] = original_coords[1]

        # 直接更新K线相关配置
        data["line_x"] = kline_centers

        # 计算K线宽度（基于相邻K线间距）
        if len(kline_centers) >= 2:
            data["line_width"] = int(0.85 * (kline_centers[1] - kline_centers[0]) / 2)
        else:
            data["line_width"] = 32  # 默认宽度

        # 计算K线范围
        lines_range = []
        for x_coord in kline_centers:
            lines_range.append([x_coord - data["line_width"], x_coord + data["line_width"]])
        data["lines_range"] = lines_range

        # 添加交互式截图的相关信息
        if "interactive_capture" not in data:
            data["interactive_capture"] = {}

        data["interactive_capture"]["last_screenshot_path"] = screenshot_path
        data["interactive_capture"]["original_selection"] = {
            "p1": original_coords[0],
            "p2": original_coords[1]
        }
        data["interactive_capture"]["kline_centers"] = kline_centers
        data["interactive_capture"]["kline_count"] = len(kline_centers)
        data["interactive_capture"]["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存配置
        with open(json_path, 'w', encoding='utf-8') as json_file:
            json.dump(data, json_file, ensure_ascii=False, indent=4)

        print(f"✓ 配置文件已更新: {json_path}")
        print(f"✓ 主要K线捕获坐标已更新:")
        print(f"  - p1 (左上角): {data['main_kline_capture_coor']['p1']}")
        print(f"  - p2 (右下角): {data['main_kline_capture_coor']['p2']}")
        print(f"✓ K线中心点坐标: {kline_centers}")
        print(f"✓ K线数量: {len(kline_centers)}")
        print(f"✓ K线宽度: {data['line_width']}")
        print(f"✓ K线范围数量: {len(data['lines_range'])}")

    except Exception as e:
        print(f"更新配置文件时发生错误: {e}")


def update_config_with_coordinates(kline_coords, screenshot_path, original_coords):
    """更新配置文件中的坐标信息（保留原有函数以兼容性）"""
    try:
        json_path = r"resources/coor_config.json"

        # 读取现有配置
        with open(json_path, 'r', encoding='utf-8') as json_file:
            data = json.load(json_file)

        # 更新K线捕获坐标
        data["main_kline_capture_coor"]["p1"] = kline_coords[0]
        data["main_kline_capture_coor"]["p2"] = kline_coords[1]

        # 添加交互式截图的相关信息
        if "interactive_capture" not in data:
            data["interactive_capture"] = {}

        data["interactive_capture"]["last_screenshot_path"] = screenshot_path
        data["interactive_capture"]["original_selection"] = {
            "p1": original_coords[0],
            "p2": original_coords[1]
        }
        data["interactive_capture"]["kline_selection"] = {
            "p1": kline_coords[0],
            "p2": kline_coords[1]
        }
        data["interactive_capture"]["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存配置
        with open(json_path, 'w', encoding='utf-8') as json_file:
            json.dump(data, json_file, ensure_ascii=False, indent=4)

        print(f"配置文件已更新: {json_path}")

    except Exception as e:
        print(f"更新配置文件时发生错误: {e}")


# perform_kline_detection 函数已被移除
# K线检测现在直接在 process_kline_selection 函数中处理


def setup_global_hotkey():
    """设置全局快捷键监听"""
    print("设置全局快捷键 Ctrl+Shift+S 用于交互式截图...")
    print("按 Ctrl+Shift+S 启动交互式截图功能")
    print("按 Ctrl+C 退出程序")

    def on_hotkey():
        """快捷键回调函数"""
        print("\n检测到快捷键 Ctrl+Shift+S，启动交互式截图...")
        try:
            # 在新线程中执行截图功能，避免阻塞快捷键监听
            screenshot_thread = threading.Thread(target=interactive_screenshot_capture)
            screenshot_thread.daemon = True
            screenshot_thread.start()
        except Exception as e:
            print(f"启动交互式截图时发生错误: {e}")

    try:
        # 注册全局快捷键
        keyboard.add_hotkey('ctrl+shift+s', on_hotkey)

        print("全局快捷键已设置成功！")
        print("程序正在后台运行，等待快捷键触发...")

        # 保持程序运行
        keyboard.wait('ctrl+c')  # 按 Ctrl+C 退出

    except Exception as e:
        print(f"设置全局快捷键时发生错误: {e}")
        print("可能需要以管理员权限运行程序")


def run_interactive_mode():
    """运行交互模式"""
    print("=" * 50)
    print("交互式屏幕截图工具")
    print("=" * 50)
    print("选择运行模式：")
    print("1. 启动全局快捷键监听模式 (Ctrl+Shift+S)")
    print("2. 立即执行交互式截图")
    print("3. 执行传统的窗口预处理流程")
    print("4. 退出")

    while True:
        try:
            choice = input("\n请选择模式 (1-4): ").strip()

            if choice == '1':
                setup_global_hotkey()
                break
            elif choice == '2':
                interactive_screenshot_capture()
                break
            elif choice == '3':
                main_traditional()
                break
            elif choice == '4':
                print("退出程序")
                break
            else:
                print("无效选择，请输入 1-4")

        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")


def main_traditional():
    """传统的窗口预处理主函数"""
    json_path = r"resources/coor_config.json"
    with open(json_path, 'r', encoding='utf-8') as json_file:
        # 2. 使用 json.load() 解析文件内容
        data = json.load(json_file)
    # 第一步，假设已经做完

    # 第二步
    image_path = r"C:\Users\<USER>\Documents\ths_autowin\resources\cache\img\60min\513050_screenshot_20250823_210541.png"
    coor_list = get_screenshot_tool.get_coor(image_path)
    data["main_kline_capture_coor"]["p1"]= coor_list[0]
    data["main_kline_capture_coor"]["p2"]= coor_list[1]
    #第三步
    resize_image_path = capture_imge.resize_img_fullimg(image_path, coor_list)

    #第四步
    # resize_image_path = r"C:\Users\<USER>\Documents\ths_gs_resource\template\cropped_kline_precessing_main_widnows.jpg"
    coors = lines_detetor.detect_and_predict_vertical_lines(resize_image_path,mode='manual')
    data["line_x"] = coors
    data["line_width"] = int(0.85*(coors[1]-coors[0])/2)
    lines_range = []
    for i in range(len(coors)):
        lines_range.append([coors[i]-data["line_width"], coors[i]+data["line_width"]])
    data["lines_range"] = lines_range
    with open(json_path, 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=4)


def main():
    """主函数 - 启动交互模式"""
    try:
        run_interactive_mode()
    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        print("尝试运行传统模式...")
        main_traditional()


if __name__ == '__main__':
    main()


