# test_image_fix.py - 测试图像处理修复
import os
import cv2
import numpy as np
from logger_manager import log_info, log_error, log_success, log_warning
import capture_imge

def test_directory_creation():
    """测试目录创建功能"""
    log_info("=== 测试目录创建功能 ===")
    
    try:
        # 测试get_img_save_path函数
        save_path_info = capture_imge.get_img_save_path("test_stock", "60min")
        log_info(f"目录路径: {save_path_info[0]}")
        log_info(f"文件名: {save_path_info[1]}")
        log_info(f"完整路径: {save_path_info[2]}")
        
        # 检查目录是否存在
        if os.path.exists(save_path_info[0]):
            log_success("目录创建成功")
        else:
            log_error("目录创建失败")
            
    except Exception as e:
        log_error(f"目录创建测试失败: {e}")

def test_image_path_handling():
    """测试图像路径处理"""
    log_info("=== 测试图像路径处理 ===")
    
    # 创建一个测试图像
    test_image = np.zeros((100, 100, 3), dtype=np.uint8)
    test_image[:, :] = [255, 0, 0]  # 蓝色图像
    
    # 获取保存路径
    save_path_info = capture_imge.get_img_save_path("test_image", "test")
    test_image_path = save_path_info[2]
    
    try:
        # 保存测试图像
        success = cv2.imwrite(test_image_path, test_image)
        if success:
            log_success(f"测试图像保存成功: {test_image_path}")
            
            # 测试读取
            loaded_image = cv2.imread(test_image_path)
            if loaded_image is not None:
                log_success("图像读取成功")
                
                # 测试使用改进的读取方法
                try:
                    with open(test_image_path, 'rb') as f:
                        image_data = f.read()
                    image_array = np.frombuffer(image_data, np.uint8)
                    decoded_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                    
                    if decoded_image is not None:
                        log_success("改进的图像读取方法成功")
                    else:
                        log_error("改进的图像读取方法失败")
                        
                except Exception as e:
                    log_error(f"改进的图像读取方法异常: {e}")
                    
            else:
                log_error("图像读取失败")
                
            # 清理测试文件
            if os.path.exists(test_image_path):
                os.remove(test_image_path)
                log_info("测试文件已清理")
                
        else:
            log_error("测试图像保存失败")
            
    except Exception as e:
        log_error(f"图像路径处理测试失败: {e}")

def test_image_cropping():
    """测试图像裁剪功能"""
    log_info("=== 测试图像裁剪功能 ===")
    
    # 创建一个测试图像
    test_image = np.random.randint(0, 255, (500, 500, 3), dtype=np.uint8)
    
    try:
        # 测试数组裁剪功能
        cropped = capture_imge.resize_img_fullimg_array(
            test_image, 
            [(50, 50), (200, 200)]
        )
        
        if cropped is not None:
            log_success(f"图像裁剪成功，裁剪后尺寸: {cropped.shape}")
            
            # 测试边界情况
            cropped_boundary = capture_imge.resize_img_fullimg_array(
                test_image,
                [(400, 400), (600, 600)]  # 超出边界
            )
            
            if cropped_boundary is not None:
                log_success(f"边界裁剪成功，裁剪后尺寸: {cropped_boundary.shape}")
            else:
                log_warning("边界裁剪失败")
                
        else:
            log_error("图像裁剪失败")
            
    except Exception as e:
        log_error(f"图像裁剪测试失败: {e}")

def test_path_normalization():
    """测试路径标准化"""
    log_info("=== 测试路径标准化 ===")
    
    try:
        # 测试不同的路径格式
        test_paths = [
            "E:\\ths_autowin\\resources\\cache\\img\\60min",
            "E:/ths_autowin/resources/cache/img/60min",
            "resources/cache/img/60min",
            "./resources/cache/img/60min"
        ]
        
        for path in test_paths:
            normalized = os.path.normpath(path)
            absolute = os.path.abspath(normalized)
            log_info(f"原路径: {path}")
            log_info(f"标准化: {normalized}")
            log_info(f"绝对路径: {absolute}")
            log_info("---")
            
    except Exception as e:
        log_error(f"路径标准化测试失败: {e}")

def test_chinese_path_handling():
    """测试中文路径处理"""
    log_info("=== 测试中文路径处理 ===")
    
    try:
        # 创建包含中文的测试目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        chinese_dir = os.path.join(current_dir, "测试目录")
        
        # 创建目录
        os.makedirs(chinese_dir, exist_ok=True)
        
        if os.path.exists(chinese_dir):
            log_success("中文目录创建成功")
            
            # 创建测试图像
            test_image = np.zeros((50, 50, 3), dtype=np.uint8)
            chinese_image_path = os.path.join(chinese_dir, "测试图像.png")
            
            # 保存图像
            success = cv2.imwrite(chinese_image_path, test_image)
            if success:
                log_success("中文路径图像保存成功")
                
                # 测试读取
                try:
                    with open(chinese_image_path, 'rb') as f:
                        image_data = f.read()
                    image_array = np.frombuffer(image_data, np.uint8)
                    decoded_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                    
                    if decoded_image is not None:
                        log_success("中文路径图像读取成功")
                    else:
                        log_error("中文路径图像读取失败")
                        
                except Exception as e:
                    log_error(f"中文路径图像读取异常: {e}")
                
                # 清理
                if os.path.exists(chinese_image_path):
                    os.remove(chinese_image_path)
                    
            else:
                log_error("中文路径图像保存失败")
                
            # 清理目录
            if os.path.exists(chinese_dir):
                os.rmdir(chinese_dir)
                log_info("测试目录已清理")
                
        else:
            log_error("中文目录创建失败")
            
    except Exception as e:
        log_error(f"中文路径处理测试失败: {e}")

def main():
    """主测试函数"""
    log_info("开始图像处理修复测试")
    log_info("=" * 50)
    
    test_directory_creation()
    test_image_path_handling()
    test_image_cropping()
    test_path_normalization()
    test_chinese_path_handling()
    
    log_success("图像处理修复测试完成")
    log_info("=" * 50)
    
    print("\n修复说明:")
    print("1. 修复了硬编码路径问题，使用相对路径")
    print("2. 添加了目录自动创建功能")
    print("3. 改进了图像读取方法，支持中文路径")
    print("4. 增加了错误处理和边界检查")
    print("5. 添加了图像数组直接处理功能")

if __name__ == "__main__":
    main()
