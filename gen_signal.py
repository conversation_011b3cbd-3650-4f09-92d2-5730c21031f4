# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

print('running on your system!!')
import capture_imge
import img_detetor
import update_sql
import sql_opeator
import pandas as pd
import csv
import os
import pandas as pd
from typing import Optional, Dict, Any
import numpy as np
import json
import cv2
from debug_config_manager import get_debug_config_manager, is_debug_mode_enabled, is_save_intermediate_enabled

class StockCSVManager:
    def __init__(self, csv_file_path: str):
        """
        初始化CSV管理器

        Args:
            csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.columns = ['code', 'name',
        'LD_flag_-15', 'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10', 'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7', 'LD_flag_-6', 'LD_flag_-5', 'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
        'LM_flag_-15', 'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10', 'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7', 'LM_flag_-6', 'LM_flag_-5', 'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1']

        # 确保目录存在
        self._ensure_directory_exists()

        # 如果文件不存在，创建一个空的CSV文件
        if not os.path.exists(csv_file_path):
            self._create_empty_csv()

    def read_json(self):
        json_path = r"resources/coor_config.json"
        with open(json_path, 'r', encoding='utf-8') as json_file:
            # 2. 使用 json.load() 解析文件内容
            self.config = json.load(json_file)

    def _ensure_directory_exists(self):
        """确保目录存在"""
        try:
            directory = os.path.dirname(self.csv_file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"已创建目录: {directory}")
        except Exception as e:
            print(f"创建目录失败: {e}")

    def _create_empty_csv(self):
        """创建空的CSV文件"""
        try:
            print(f"正在创建CSV文件: {self.csv_file_path}")
            with open(self.csv_file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(self.columns)
            print(f"CSV文件创建成功，列数: {len(self.columns)}")
        except Exception as e:
            print(f"创建CSV文件失败: {e}")
            raise

    def _read_csv(self) -> pd.DataFrame:
        """读取CSV文件"""
        try:
            if not os.path.exists(self.csv_file_path):
                print(f"CSV文件不存在: {self.csv_file_path}")
                return pd.DataFrame(columns=self.columns)

            # 指定code列为字符串类型读取，保留前导零
            dtype_dict = {'code': str}
            df = pd.read_csv(self.csv_file_path, encoding='utf-8', dtype=dtype_dict)
            print(f"成功读取CSV文件，行数: {len(df)}")

            # 确保所有必需的列都存在
            for col in self.columns:
                if col not in df.columns:
                    df[col] = None
                    print(f"添加缺失列: {col}")
            return df
        except Exception as e:
            print(f"读取CSV文件出错: {e}")
            return pd.DataFrame(columns=self.columns)

    def _write_csv(self, df: pd.DataFrame):
        """写入CSV文件"""
        try:
            print(f"正在写入CSV文件: {self.csv_file_path}")
            print(f"待写入数据行数: {len(df)}")

            # 确保股票代码列保持为字符串格式，保留前导零
            if 'code' in df.columns:
                df['code'] = df['code'].astype(str)
                # 确保6位股票代码格式（如果是数字，补齐前导零）
                df['code'] = df['code'].apply(lambda x: str(x).zfill(6) if str(x).isdigit() else str(x))

            df.to_csv(self.csv_file_path, index=False, encoding='utf-8')
            print("CSV文件写入成功")
        except Exception as e:
            print(f"写入CSV文件出错: {e}")
            print(f"错误详情: {str(e)}")
            raise

    def update_stock(self, code: str, name: str, ld_flag, lm_flag):
        """
        更新或新增股票数据

        Args:
            code: 股票代码
            name: 股票名称
            ld_flag: LD标志（可以是单个值或列表）
            lm_flag: LM标志（可以是单个值或列表）
        """
        print(f"\n=== 更新股票数据 ===")

        # 确保股票代码为字符串格式并保留前导零
        code = str(code).zfill(6) if str(code).isdigit() else str(code)

        print(f"股票代码: {code}")
        print(f"股票名称: {name}")
        print(f"LD标志: {ld_flag} (类型: {type(ld_flag)})")
        print(f"LM标志: {lm_flag} (类型: {type(lm_flag)})")

        # 参数验证
        if not code or not name:
            print("错误: 股票代码和名称不能为空")
            return

        if ld_flag is None or lm_flag is None:
            print("错误: LD_flag和LM_flag不能为None")
            return

        df = self._read_csv()

        # 确保DataFrame中的code列也是字符串格式
        if len(df) > 0 and 'code' in df.columns:
            df['code'] = df['code'].astype(str)
            # 对现有的数字代码也补齐前导零
            df['code'] = df['code'].apply(lambda x: str(x).zfill(6) if str(x).isdigit() else str(x))

        # 查找是否存在该股票 - 使用字符串比较
        stock_exists_mask = df['code'] == code
        stock_exists = stock_exists_mask.any()

        print(f"DataFrame中现有股票代码: {df['code'].tolist() if len(df) > 0 else '无'}")
        print(f"查找股票 {code} 是否存在: {stock_exists}")

        if stock_exists:
            print(f"股票 {code} 已存在，正在更新...")
            # 更新现有股票数据
            df.loc[stock_exists_mask, 'name'] = name

            # 处理LD标志
            if isinstance(ld_flag, (list, tuple, np.ndarray)):
                print(f"LD标志是列表，长度: {len(ld_flag)}")
                for i in range(min(len(ld_flag), 15)):
                    col_name = f'LD_flag_{i-15}'
                    df.loc[stock_exists_mask, col_name] = ld_flag[i]
                    # print(f"更新 {col_name} = {ld_flag[i]}")
            else:
                print(f"LD标志是单个值: {ld_flag}")
                # 如果是单个值，更新所有LD列
                for i in range(15):
                    col_name = f'LD_flag_{i-15}'
                    df.loc[stock_exists_mask, col_name] = ld_flag

            # 处理LM标志
            if isinstance(lm_flag, (list, tuple, np.ndarray)):
                print(f"LM标志是列表，长度: {len(lm_flag)}")
                for i in range(min(len(lm_flag), 15)):
                    col_name = f'LM_flag_{i-15}'
                    df.loc[stock_exists_mask, col_name] = lm_flag[i]
                    # print(f"更新 {col_name} = {lm_flag[i]}")
            else:
                print(f"LM标志是单个值: {lm_flag}")
                # 如果是单个值，更新所有LM列
                for i in range(15):
                    col_name = f'LM_flag_{i-15}'
                    df.loc[stock_exists_mask, col_name] = lm_flag

            print(f"已更新股票 {code} 的数据")
        else:
            print(f"股票 {code} 不存在，正在新增...")
            # 新增股票数据
            data_flag = {}
            data_flag['code'] = code  # 已经是格式化后的字符串
            data_flag['name'] = name

            # 处理LD标志
            if isinstance(ld_flag, (list, tuple, np.ndarray)):
                print(f"LD标志是列表，长度: {len(ld_flag)}")
                for i in range(min(len(ld_flag), 15)):
                    data_flag[f'LD_flag_{i-15}'] = ld_flag[i]
                # 如果列表长度不足15，用None填充
                for i in range(len(ld_flag), 15):
                    data_flag[f'LD_flag_{i-15}'] = None
            else:
                print(f"LD标志是单个值: {ld_flag}")
                # 如果是单个值，设置所有LD列
                for i in range(15):
                    data_flag[f'LD_flag_{i-15}'] = ld_flag

            # 处理LM标志
            if isinstance(lm_flag, (list, tuple, np.ndarray)):
                print(f"LM标志是列表，长度: {len(lm_flag)}")
                for i in range(min(len(lm_flag), 15)):
                    data_flag[f'LM_flag_{i-15}'] = lm_flag[i]
                # 如果列表长度不足15，用None填充
                for i in range(len(lm_flag), 15):
                    data_flag[f'LM_flag_{i-15}'] = None
            else:
                print(f"LM标志是单个值: {lm_flag}")
                # 如果是单个值，设置所有LM列
                for i in range(15):
                    data_flag[f'LM_flag_{i-15}'] = lm_flag

            print(f"新行数据键数: {len(data_flag)}")
            new_row = pd.DataFrame([data_flag])
            df = pd.concat([df, new_row], ignore_index=True)
            print(f"已新增股票 {code} 的数据")

        # 写入文件前再次确认没有重复
        print(f"写入前检查重复: {df['code'].value_counts()}")
        df_deduplicated = df.drop_duplicates(subset=['code'], keep='last')
        if len(df_deduplicated) != len(df):
            print(f"发现并移除了 {len(df) - len(df_deduplicated)} 个重复记录")
            df = df_deduplicated

        # 写入文件
        print(f"准备写入文件，总行数: {len(df)}")
        self._write_csv(df)

    def get_stock(self, code: str) -> Optional[Dict[str, Any]]:
        """
        获取指定股票的数据

        Args:
            code: 股票代码

        Returns:
            股票数据字典，如果不存在返回None
        """
        df = self._read_csv()
        stock_data = df[df['code'] == code]

        if not stock_data.empty:
            return stock_data.iloc[0].to_dict()
        return None

    def list_all_stocks(self) -> pd.DataFrame:
        """获取所有股票数据"""
        return self._read_csv()

    def delete_stock(self, code: str):
        """
        删除指定股票

        Args:
            code: 股票代码
        """
        df = self._read_csv()
        original_len = len(df)
        df = df[df['code'] != code]

        if len(df) < original_len:
            self._write_csv(df)
            print(f"已删除股票 {code}")
        else:
            print(f"股票 {code} 不存在")

    def cleanup_zero_flags(self):
        """清理所有LD_flag=LM_flag=0的股票"""
        df = self._read_csv()
        original_len = len(df)

        # 检查所有LD和LM标志列是否都为0
        ld_cols = [col for col in df.columns if col.startswith('LD_flag_')]
        lm_cols = [col for col in df.columns if col.startswith('LM_flag_')]

        # 创建条件：所有LD标志都为0且所有LM标志都为0
        ld_all_zero = (df[ld_cols] == 0).all(axis=1)
        lm_all_zero = (df[lm_cols] == 0).all(axis=1)

        df = df[~(ld_all_zero & lm_all_zero)]

        deleted_count = original_len - len(df)
        if deleted_count > 0:
            self._write_csv(df)
            print(f"已清理 {deleted_count} 个标志为0的股票")
        else:
            print("没有需要清理的股票")

def keep_last_status(stock_value_dict):
    """保持上次状态"""
    manager = StockCSVManager(r"C:\Users\<USER>\Documents\ths_gs_resource\status_signal.csv")
    res = manager.get_stock(stock_value_dict['code'])
    if res:
        # 修正：应该从res中获取相应的标志值
        ld_flags = []
        lm_flags = []

        # 提取LD标志
        for i in range(-14, 0):
            key = f'LD_flag_{i}'
            if key in res:
                value = res[key]
                # 如果 value 不是 NaN，则添加 value 本身，否则添加 0
                ld_flags.append(value if not np.isnan(value) else 0.0)

        # 提取LM标志
        for i in range(-14, 0):
            key = f'LM_flag_{i}'
            if key in res:
                value = res[key]
                # 如果 value 不是 NaN，则添加 value 本身，否则添加 0
                lm_flags.append(value if not np.isnan(value) else 0.0)

        stock_value_dict['LD_flag_list'] = ld_flags
        stock_value_dict['LM_flag_list'] = lm_flags

    return stock_value_dict

def keep_last_status_sql(stock_value_dict):
    """保持上次状态"""
    sql_manager = sql_opeator.StockDataOperations(table_name='status_signal')
    full_data = [
        ["000001", "平安银行", 1, 0, None, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
        ["000002", "万科A", 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]
    ]
    try:
        res = sql_manager.query_fields(
            stock_value_dict['field'],
            where_conditions={'code': stock_value_dict['code']}
        )[0]
    except:
        sql_manager.upsert_stock_data(full_data)
        res = sql_manager.query_fields(
            stock_value_dict['field'],
            where_conditions={'code': "000001"}
        )[0]
    if res:
        # 修正：应该从res中获取相应的标志值
        ld_flags = []
        lm_flags = []

        # 提取LD标志
        for i in range(-14, 0):
            key = f'LD_flag_{i}'
            if key in res:
                value = np.array(res[key])
                # 如果 value 不是 NaN，则添加 value 本身，否则添加 0
                # 安全判断 value 是否为 NaN 或 None
                if value is None:
                    ld_flags.append(0.0)
                else:
                    try:
                        v = float(value)
                        ld_flags.append(v if not np.isnan(v) else 0.0)
                    except (TypeError, ValueError):
                        ld_flags.append(0.0)

        # 提取LM标志
        for i in range(-14, 0):
            key = f'LM_flag_{i}'
            if key in res:
                value = np.array(res[key])
                # 如果 value 不是 NaN，则添加 value 本身，否则添加 0
                if value is None:
                    lm_flags.append(0.0)
                else:
                    try:
                        v = float(value)
                        lm_flags.append(v if not np.isnan(v) else 0.0)
                    except (TypeError, ValueError):
                        lm_flags.append(0.0)

        stock_value_dict['LD_flag_list'] = ld_flags
        stock_value_dict['LM_flag_list'] = lm_flags

    return stock_value_dict

def _process_with_debug_images(image_bgr, cropped_bgr, stock_value_dict, config, debug_config):
    """
    调试模式：保存中间图像并进行处理

    Args:
        image_bgr: 原始图像数组
        cropped_bgr: 裁剪后的图像数组
        stock_value_dict: 股票信息字典
        config: 配置信息
        debug_config: 调试配置

    Returns:
        flag_list: 检测结果列表
    """
    import cv2
    from datetime import datetime

    # 获取调试配置
    debug_dir = debug_config.get("debug_output_dir", "resources/cache/debug")
    save_original = debug_config.get("save_original_screenshot", True)
    save_cropped = debug_config.get("save_cropped_image", True)
    save_detection = debug_config.get("save_detection_result", True)
    image_format = debug_config.get("image_format", "png")
    add_timestamp = debug_config.get("add_timestamp_to_filename", True)

    # 确保调试目录存在
    os.makedirs(debug_dir, exist_ok=True)

    # 生成文件名
    stock_code = stock_value_dict.get('code', 'unknown')
    if add_timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
        base_filename = f"{stock_code}_{timestamp}"
    else:
        base_filename = stock_code

    print(f"[调试模式] 保存股票 {stock_code} 的调试图像到: {debug_dir}")

    try:
        # 保存原始截图
        if save_original and image_bgr is not None:
            original_path = os.path.join(debug_dir, f"{base_filename}_original.{image_format}")
            cv2.imwrite(original_path, image_bgr)
            print(f"[调试] 原始截图已保存: {original_path}")

        # 保存裁剪后的图像
        if save_cropped and cropped_bgr is not None:
            cropped_path = os.path.join(debug_dir, f"{base_filename}_cropped.{image_format}")
            cv2.imwrite(cropped_path, cropped_bgr)
            print(f"[调试] 裁剪图像已保存: {cropped_path}")

        # 进行图标检测
        flag_list = img_detetor.img_search_ico_array(cropped_bgr, config["lines_range"])

        # 保存检测结果图像（如果img_detetor支持）
        if save_detection:
            try:
                # 尝试生成带检测结果的图像
                detection_result_image = _create_detection_result_image(
                    cropped_bgr, flag_list, config["lines_range"]
                )
                if detection_result_image is not None:
                    detection_path = os.path.join(debug_dir, f"{base_filename}_detection.{image_format}")
                    cv2.imwrite(detection_path, detection_result_image)
                    print(f"[调试] 检测结果图像已保存: {detection_path}")
            except Exception as e:
                print(f"[调试] 保存检测结果图像失败: {e}")

        # 保存检测结果到文本文件
        result_txt_path = os.path.join(debug_dir, f"{base_filename}_result.txt")
        with open(result_txt_path, 'w', encoding='utf-8') as f:
            f.write(f"股票代码: {stock_code}\n")
            f.write(f"股票名称: {stock_value_dict.get('name', 'unknown')}\n")
            f.write(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"检测结果: {flag_list}\n")
            f.write(f"信号数量: {sum(1 for flag in flag_list if flag != 0)}\n")
            f.write(f"检测区域: {config['lines_range']}\n")
        print(f"[调试] 检测结果已保存: {result_txt_path}")

        return flag_list

    except Exception as e:
        print(f"[调试模式] 处理调试图像时发生错误: {e}")
        # 如果调试模式失败，回退到内存模式
        return img_detetor.img_search_ico_array(cropped_bgr, config["lines_range"])

def _create_detection_result_image(image, flag_list, lines_range):
    """
    创建带检测结果标注的图像

    Args:
        image: 输入图像
        flag_list: 检测结果列表
        lines_range: 检测区域范围

    Returns:
        标注后的图像
    """
    import cv2

    try:
        # 复制图像以避免修改原图
        result_image = image.copy()

        # 在检测区域绘制矩形框和结果
        for i, (flag, line_range) in enumerate(zip(flag_list, lines_range)):
            if len(line_range) >= 2:
                x1, x2 = line_range[0], line_range[1]
                y1, y2 = 0, image.shape[0]  # 使用整个高度

                # 根据检测结果选择颜色
                if flag != 0:
                    color = (0, 255, 0)  # 绿色表示检测到信号
                    thickness = 2
                else:
                    color = (128, 128, 128)  # 灰色表示无信号
                    thickness = 1

                # 绘制矩形框
                cv2.rectangle(result_image, (x1, y1), (x2, y2), color, thickness)

                # 添加文本标签
                label = f"{i+1}:{flag}"
                cv2.putText(result_image, label, (x1, y1-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        return result_image

    except Exception as e:
        print(f"创建检测结果图像失败: {e}")
        return None

def deal_signal_60mins(stock_value_dict, config_path):

    json_path = config_path
    with open(json_path, 'r', encoding='utf-8') as json_file:
        # 2. 使用 json.load() 解析文件内容
        config = json.load(json_file)
    """处理60分钟信号"""
    stock_name = stock_value_dict['name']
    stock_code = stock_value_dict['code']

    # 优先走内存：直接读取原图为数组并裁剪
    image_path = stock_value_dict['img_save_path']

    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误: 截图文件不存在: {image_path}")
        return [0, "文件不存在"]

    try:
        # 使用改进的图像读取方法
        image_path_abs = os.path.abspath(image_path)
        with open(image_path_abs, 'rb') as f:
            image_data = f.read()
        image_array = np.frombuffer(image_data, np.uint8)
        image_bgr = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

        if image_bgr is None:
            print(f"错误: 无法解码图像文件: {image_path}")
            return [0, "图像解码失败"]

    except Exception as e:
        print(f"读取图像文件时发生错误: {e}")
        print(f"文件路径: {image_path}")
        return [0, f"读取错误: {str(e)}"]

    # 裁剪图像
    cropped_bgr = capture_imge.resize_img_fullimg_array(
        image_bgr,
        [config["main_kline_capture_coor"]["p1"], config["main_kline_capture_coor"]["p2"]]
    )

    if cropped_bgr is None:
        print("错误: 图像裁剪失败")
        return [0, "图像裁剪失败"]

    # 根据配置决定是否保存调试图像
    debug_manager = get_debug_config_manager()
    enable_debug = debug_manager.is_debug_enabled()
    save_intermediate = debug_manager.is_save_intermediate_enabled()

    if enable_debug and save_intermediate:
        # 调试模式：保存中间图像到磁盘
        print(f"[调试模式] 启用调试模式处理股票 {stock_value_dict.get('code', 'unknown')}")
        flag_list = _process_with_debug_images(
            image_bgr, cropped_bgr, stock_value_dict, config, debug_manager.get_debug_config()
        )
    else:
        # 生产模式：直接在内存数组上做图标检测，避免磁盘读写
        print(f"[生产模式] 内存处理股票 {stock_value_dict.get('code', 'unknown')}")
        flag_list = img_detetor.img_search_ico_array(cropped_bgr, config["lines_range"], save = False)

    # 如仍需删除原图文件（可选）
    image_path = stock_value_dict['img_save_path']
    if os.path.exists(image_path):
        os.remove(image_path)

    stock_value_dict['LM_flag_list'] = flag_list
    new_data_list = []
    new_data_list.append(stock_code)
    new_data_list.append(stock_name)
    print(f"LD_flag_list:{stock_value_dict['LD_flag_list']}")
    print(f"LM_flag_list:{stock_value_dict['LM_flag_list']}")
    # 使用 np.concatenate() 进行拼接
    new_data_list = np.concatenate((new_data_list, stock_value_dict['LD_flag_list']))
    new_data_list = np.concatenate((new_data_list, stock_value_dict['LM_flag_list']))
    # 修正：检查flag_list是否全为0
    if isinstance(flag_list, (list, tuple, np.ndarray)):
        if all(flag == 0 for flag in flag_list):
            bool_flag = 0
        else:
            bool_flag = 1
    else:
        bool_flag = 1 if flag_list != 0 else 0
    # update_sql.upsert_stock_data([new_data_list])

    return bool_flag, new_data_list

def deal_signal_days(stock_value_dict):
    """处理日信号"""
    stock_name = stock_value_dict['name']

def deal_signal_60mins_array(stock_value_dict, config_path, main_image_bgr):
    """处理60分钟信号（内存版，不做磁盘读写）"""
    with open(config_path, 'r', encoding='utf-8') as json_file:
        config = json.load(json_file)

    stock_name = stock_value_dict['name']
    stock_code = stock_value_dict['code']

    # 内存裁剪
    cropped_bgr = capture_imge.resize_img_fullimg_array(
        main_image_bgr,
        [config["main_kline_capture_coor"]["p1"], config["main_kline_capture_coor"]["p2"]]
    )

    flag_list = img_detetor.img_search_ico_array(cropped_bgr, config["lines_range"])
    stock_value_dict['LM_flag_list'] = flag_list

    new_data_list = []
    new_data_list.append(stock_code)
    new_data_list.append(stock_name)
    print(f"LD_flag_list:{stock_value_dict['LD_flag_list']}")
    print(f"LM_flag_list:{stock_value_dict['LM_flag_list']}")
    new_data_list = np.concatenate((new_data_list, stock_value_dict['LD_flag_list']))
    new_data_list = np.concatenate((new_data_list, stock_value_dict['LM_flag_list']))

    if isinstance(flag_list, (list, tuple, np.ndarray)):
        bool_flag = 0 if all(flag == 0 for flag in flag_list) else 1
    else:
        bool_flag = 1 if flag_list != 0 else 0

    return bool_flag, new_data_list

    stock_code = stock_value_dict['code']
    cropped_path = capture_imge.resize_img_fullimg(stock_value_dict['img_save_path'])
    flag = img_detetor.img_search_ico(cropped_path)

    # 删除原截图文件和resize的文件
    # image_path = stock_value_dict['img_save_path']
    # if os.path.exists(image_path):
    #     os.remove(image_path)
    #     print("图片已删除")
    # else:
    #     print("图片不存在")

    # image_path = cropped_path
    # if os.path.exists(image_path):
    #     os.remove(image_path)
    #     print("图片已删除")
    # else:
    #     print("图片不存在")

    stock_value_dict['LM_flag'] = flag
    stock_value_dict['LD_flag'] = flag
    print(f"1 Day 信号为{flag}")

    manager = StockCSVManager(r"C:\Users\<USER>\Documents\ths_gs_resource\status_signal.csv")
    manager.update_stock(stock_code, stock_name, stock_value_dict['LD_flag'], stock_value_dict['LM_flag'])

    return flag

def test_csv_functionality():
    """测试CSV功能的函数"""
    print("=== 开始CSV功能测试 ===")

    # 使用一个测试路径
    test_csv_path = "test_stock_data.csv"

    try:
        # 创建管理器实例
        print("1. 创建CSV管理器...")
        manager = StockCSVManager(test_csv_path)

        # 测试前导零问题
        print("\n2. 测试前导零股票代码...")
        manager.update_stock("000111", "前导零测试1", 1, 0)
        manager.update_stock("000001", "前导零测试2", 1, 1)

        # 测试重复更新
        print("\n3. 测试更新现有股票（应该不产生重复）...")
        manager.update_stock("000111", "前导零测试1_更新", 0, 1)

        # 测试列表输入
        print("\n4. 测试列表输入...")
        manager.update_stock("000002", "测试股票2", [1, 0, 1], [0, 1, 0])

        # 再次更新同一只股票
        print("\n5. 再次更新同一只股票...")
        manager.update_stock("000002", "测试股票2_更新", [0, 1, 0], [1, 0, 1])

        # 读取所有数据
        print("\n6. 读取所有数据...")
        df = manager.list_all_stocks()
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")

        if len(df) > 0:
            print("所有股票代码:")
            for idx, row in df.iterrows():
                print(f"  {row['code']} - {row['name']}")

            print("\n检查重复:")
            duplicates = df['code'].value_counts()
            print(duplicates)

            if (duplicates > 1).any():
                print("警告: 发现重复的股票代码!")
            else:
                print("✓ 没有重复的股票代码")

        # 清理测试文件
        if os.path.exists(test_csv_path):
            os.remove(test_csv_path)
            print("\n7. 测试文件已清理")

        print("\n=== CSV功能测试完成 ===")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main(args):
    # # 添加测试调用
    # print("执行CSV功能测试...")
    # test_csv_functionality()
    # pass
    stock_value_dict = {}
    stock_value_dict['code'] = '000001'
    stock_value_dict['name'] = 'pk'
    stock_value_dict['img_save_path'] = r"234.jpg"
    stock_value_dict['LD_flag_list'] = np.zeros(14)
    stock_value_dict['LM_flag_list'] = np.zeros(14)
    stock_value_dict['field'] = [
    'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10', 'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7',
    'LD_flag_-6', 'LD_flag_-5', 'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
    'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10', 'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7',
    'LM_flag_-6', 'LM_flag_-5', 'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1']
    keep_last_status_sql(stock_value_dict)

if __name__ == '__main__':
    stock_value_dict = {}
    stock_value_dict['code'] = '000001'
    stock_value_dict['name'] = 'pk'
    stock_value_dict['img_save_path'] = r"234.jpg"
    stock_value_dict['LD_flag_list'] = np.zeros(14)
    stock_value_dict['LM_flag_list'] = np.zeros(14)
    stock_value_dict['field'] = [
    'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10', 'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7',
    'LD_flag_-6', 'LD_flag_-5', 'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
    'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10', 'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7',
    'LM_flag_-6', 'LM_flag_-5', 'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1']
    keep_last_status_sql(stock_value_dict)