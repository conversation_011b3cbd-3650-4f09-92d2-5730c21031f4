{"openedFlows": ["main", "open_ths_window", "get_stock_pool", "遍历日线", "遍历60min", "capture_imge", "img_detetor", "gen_signal", "save_data_to_sql", "lines_detetor", "task_main", "update_sql", "获取THS截图", "sql_opeator", "pyauto_opeator_windows"], "activeBottomPanel": "运行日志", "activeFlow": "task_main", "breakPoints": [{"FlowName": "main", "Lines": null, "BlockIds": []}, {"FlowName": "open_ths_window", "Lines": null, "BlockIds": ["f1d71591-72f7-49cd-847c-fd407e680373"]}, {"FlowName": "get_stock_pool", "Lines": [], "BlockIds": null}, {"FlowName": "遍历日线", "Lines": null, "BlockIds": []}, {"FlowName": "遍历60min", "Lines": null, "BlockIds": []}, {"FlowName": "capture_imge", "Lines": [530], "BlockIds": null}, {"FlowName": "img_detetor", "Lines": [], "BlockIds": null}, {"FlowName": "gen_signal", "Lines": [], "BlockIds": null}, {"FlowName": "子流程1", "Lines": null, "BlockIds": []}, {"FlowName": "save_data_to_sql", "Lines": null, "BlockIds": []}, {"FlowName": "窗口坐标预处理", "Lines": null, "BlockIds": []}, {"FlowName": "lines_detetor", "Lines": [], "BlockIds": null}, {"FlowName": "task_main", "Lines": [], "BlockIds": null}, {"FlowName": "update_sql", "Lines": [], "BlockIds": null}, {"FlowName": "获取THS截图", "Lines": null, "BlockIds": []}, {"FlowName": "sql_opeator", "Lines": [], "BlockIds": null}, {"FlowName": "pyauto_opeator_windows", "Lines": [], "BlockIds": null}]}