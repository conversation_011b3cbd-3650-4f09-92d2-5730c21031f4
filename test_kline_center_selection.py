#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线中心点选择功能测试脚本
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_kline_center_selection():
    """测试K线中心点选择功能"""
    print("=" * 60)
    print("K线中心点选择功能测试")
    print("=" * 60)
    
    print("新的K线选择流程：")
    print("1. 交互式选择屏幕区域进行截图")
    print("2. 直接在截图上点击K线中心点")
    print("3. 自动计算K线范围和宽度")
    print("4. 更新配置文件")
    print()
    
    print("与传统方法的区别：")
    print("✓ 无需选择K线区域的左上角和右下角")
    print("✓ 直接点击K线中心点即可")
    print("✓ 自动计算K线宽度和范围")
    print("✓ 流程更加简化")
    print()
    
    input("按回车键开始测试...")
    
    try:
        # 导入功能模块
        from windows_precessing import interactive_screenshot_capture
        
        print("正在启动交互式截图功能...")
        print("请按照以下步骤操作：")
        print("1. 在全屏覆盖层上拖拽选择包含K线的区域")
        print("2. 按回车确认选择")
        print("3. 在弹出的窗口中点击K线的中心点（建议选择15个点）")
        print("4. 完成后按任意键或等待自动完成")
        print()
        
        # 执行交互式截图
        result = interactive_screenshot_capture()
        
        if result:
            print(f"\n✓ 测试成功完成！")
            print(f"✓ 截图已保存到: {result}")
            
            # 检查配置文件更新
            check_config_update()
            
        else:
            print("\n测试被取消或失败")
            
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保 windows_precessing.py 文件存在且正确")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

def check_config_update():
    """检查配置文件更新情况"""
    try:
        config_path = "resources/coor_config.json"
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("\n配置文件更新情况：")
            
            # 检查K线坐标
            if "line_x" in config:
                line_x = config["line_x"]
                print(f"✓ K线x坐标: {line_x}")
                print(f"✓ K线数量: {len(line_x)}")
            
            # 检查K线宽度
            if "line_width" in config:
                print(f"✓ K线宽度: {config['line_width']}")
            
            # 检查K线范围
            if "lines_range" in config:
                lines_range = config["lines_range"]
                print(f"✓ K线范围数量: {len(lines_range)}")
                if lines_range:
                    print(f"✓ 第一个K线范围: {lines_range[0]}")
                    if len(lines_range) > 1:
                        print(f"✓ 最后一个K线范围: {lines_range[-1]}")
            
            # 检查交互式截图信息
            if "interactive_capture" in config:
                interactive_config = config["interactive_capture"]
                
                if "timestamp" in interactive_config:
                    print(f"✓ 更新时间: {interactive_config['timestamp']}")
                
                if "kline_centers" in interactive_config:
                    centers = interactive_config["kline_centers"]
                    print(f"✓ K线中心点: {centers}")
                
                if "kline_count" in interactive_config:
                    print(f"✓ 选择的K线数量: {interactive_config['kline_count']}")
            
        else:
            print("❌ 配置文件不存在")
            
    except Exception as e:
        print(f"❌ 读取配置文件时发生错误: {e}")

def test_traditional_vs_interactive():
    """对比传统方法和交互式方法"""
    print("=" * 60)
    print("传统方法 vs 交互式方法对比")
    print("=" * 60)
    
    comparison = [
        ("步骤", "传统方法", "交互式方法"),
        ("1. 获取图片", "手动指定图片路径", "实时屏幕截图"),
        ("2. 区域选择", "选择K线区域左上角和右下角", "拖拽选择截图区域"),
        ("3. 图像处理", "手动调用裁剪函数", "自动处理"),
        ("4. K线选择", "在裁剪后的图像上选择中心点", "直接在截图上选择中心点"),
        ("5. 配置更新", "分步更新配置", "一次性更新所有配置"),
        ("用户操作", "多步骤，需要输入路径", "一键完成，全程可视化"),
        ("效率", "较低，需要多次切换", "高效，流程连贯"),
        ("易用性", "需要技术背景", "用户友好"),
    ]
    
    # 打印对比表格
    for i, (step, traditional, interactive) in enumerate(comparison):
        if i == 0:  # 表头
            print(f"{'步骤':<15} {'传统方法':<25} {'交互式方法':<25}")
            print("-" * 65)
        else:
            print(f"{step:<15} {traditional:<25} {interactive:<25}")
    
    print("\n优势总结：")
    print("✓ 交互式方法更加直观和用户友好")
    print("✓ 减少了手动输入和文件路径管理")
    print("✓ 流程更加连贯，减少了上下文切换")
    print("✓ 自动化程度更高，减少了出错可能")
    print("✓ 支持全局快捷键，随时可用")

def main():
    """主函数"""
    print("K线中心点选择功能测试工具")
    print("=" * 40)
    
    while True:
        print("\n选择测试模式：")
        print("1. 测试K线中心点选择功能")
        print("2. 查看传统方法 vs 交互式方法对比")
        print("3. 查看配置文件当前状态")
        print("4. 退出")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                test_kline_center_selection()
            elif choice == '2':
                test_traditional_vs_interactive()
            elif choice == '3':
                check_config_update()
            elif choice == '4':
                print("退出测试")
                break
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
