###
import open_ths_pyauto
import os
import time

def sub_cycle_body(window_instance, stock, period = "35"):
    # 确保窗口激活
    status = open_ths_pyauto.activate_window_to_front(window_instance, force_activate=True)    
    # 向屏幕的输入股票代码，然后按回车
    # open_ths_pyauto.click_button_input_by_coor(stock[0], press_enter=True)
    # open_ths_pyauto.click_search_box(window_instance, search_text=stock[0], press_enter=True)
    # time.sleep(1)
    # 判断是不是60min分钟线, 如果不是，则输入35
    current_dir = os.path.dirname(os.path.abspath(__file__))
    template_path = os.path.join(current_dir, "resources", "images", "tuoying_title.png")
    match_result = open_ths_pyauto.locate_template_on_screen(template_path, confidence= None, region=None)
    if match_result['found'] is False:
        status = open_ths_pyauto.click_search_box(window_instance, search_text="35", press_enter=True)
        time.sleep(0.5)
        open_ths_pyauto.click_search_box(window_instance, search_text=stock[0], press_enter=True)
        time.sleep(1)