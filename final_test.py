"""
最终测试脚本 - 验证所有功能
"""
import pyautogui
import time
from open_ths_pyauto import open_tonghuashun_complete

def main():
    """
    主测试函数
    """
    print("=== 同花顺自动化操作最终测试 ===")
    print()
    
    # 设置安全选项
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    
    print("注意事项：")
    print("1. 请确保同花顺软件路径正确")
    print("2. 测试过程中请不要移动鼠标或按键")
    print("3. 如需紧急停止，将鼠标移到屏幕左上角")
    print("4. 请关闭其他可能干扰的程序")
    print()
    
    # 倒计时
    print("测试将在5秒后开始...")
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("开始测试！")
    print()
    
    try:
        # 执行完整的操作流程
        window = open_tonghuashun_complete()
        
        if window:
            print()
            print("🎉 测试成功！")
            print("✅ 同花顺软件已启动")
            print("✅ 窗口已调整到指定大小和位置")
            print("✅ 已输入 '35'")
            print("✅ 已按下回车键")
            print(f"✅ 窗口句柄：{window.handle}")
            
            # 额外验证
            print()
            print("请检查同花顺窗口：")
            print("- 窗口是否在屏幕左上角")
            print("- 窗口大小是否为 1920x1080")
            print("- 是否已输入 '35' 并执行了搜索")
            
            return True
        else:
            print()
            print("❌ 测试失败！")
            print("请检查：")
            print("- 同花顺软件路径是否正确")
            print("- 是否有足够的权限")
            print("- 软件是否能正常启动")
            
            return False
            
    except KeyboardInterrupt:
        print()
        print("⚠️ 测试被用户中断")
        return False
    except Exception as e:
        print()
        print(f"❌ 测试过程中发生错误：{str(e)}")
        return False

def test_individual_components():
    """
    测试各个组件
    """
    from open_ths_pyauto import (
        launch_tonghuashun_process,
        connect_to_window,
        resize_and_move_window,
        input_text_to_window,
        press_enter
    )
    
    print("=== 组件测试模式 ===")
    print()
    
    # 测试1：启动进程
    print("📋 测试1：启动同花顺进程")
    process = launch_tonghuashun_process()
    if not process:
        print("❌ 进程启动失败")
        return False
    print("✅ 进程启动成功")
    print()
    
    # 测试2：连接窗口
    print("📋 测试2：连接到窗口")
    window = connect_to_window(process)
    if not window:
        print("❌ 窗口连接失败")
        return False
    print("✅ 窗口连接成功")
    print()
    
    # 测试3：调整窗口
    print("📋 测试3：调整窗口大小和位置")
    resize_success = resize_and_move_window(window, 0, 0, 1920, 1080)
    print(f"{'✅' if resize_success else '⚠️'} 窗口调整{'成功' if resize_success else '部分成功'}")
    print()
    
    # 等待用户确认
    input("请确认窗口已正确调整，然后按回车继续...")
    
    # 测试4：输入文本
    print("📋 测试4：输入文本 '35'")
    input_success = input_text_to_window(window, '35')
    print(f"{'✅' if input_success else '❌'} 文本输入{'成功' if input_success else '失败'}")
    print()
    
    # 等待用户确认
    input("请确认是否看到输入的 '35'，然后按回车继续...")
    
    # 测试5：按回车
    print("📋 测试5：按回车键")
    enter_success = press_enter()
    print(f"{'✅' if enter_success else '❌'} 回车键{'成功' if enter_success else '失败'}")
    print()
    
    print("=== 组件测试完成 ===")
    return True

if __name__ == "__main__":
    print("选择测试模式：")
    print("1. 完整自动测试")
    print("2. 分步组件测试")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            main()
            break
        elif choice == "2":
            test_individual_components()
            break
        elif choice == "3":
            print("退出测试")
            break
        else:
            print("无效选择，请重新输入")
