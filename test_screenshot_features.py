"""
测试截图和模板匹配功能
"""

import os
import sys
import time
from open_ths_pyauto import *

def test_dependencies():
    """
    测试依赖包是否正确安装
    """
    print("=== 测试依赖包 ===")
    
    try:
        import cv2
        print(f"✓ OpenCV 版本: {cv2.__version__}")
    except ImportError:
        print("✗ OpenCV 未安装，请运行: pip install opencv-python")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy 版本: {np.__version__}")
    except ImportError:
        print("✗ NumPy 未安装，请运行: pip install numpy")
        return False
    
    try:
        from PIL import Image
        print(f"✓ Pillow 已安装")
    except ImportError:
        print("✗ Pillow 未安装，请运行: pip install Pillow")
        return False
    
    try:
        import win32gui
        print("✓ pywin32 已安装")
    except ImportError:
        print("✗ pywin32 未安装，请运行: pip install pywin32")
        return False

    try:
        import pyautogui
        print(f"✓ pyautogui 已安装")
    except ImportError:
        print("✗ pyautogui 未安装，请运行: pip install pyautogui")
        return False

    print("所有依赖包检查通过!")
    return True

def test_screenshot_function():
    """
    测试截图功能
    """
    print("\n=== 测试截图功能 ===")
    
    # 启动同花顺
    print("启动同花顺...")
    process = launch_tonghuashun_process()
    if not process:
        print("✗ 无法启动同花顺")
        return False
    
    # 连接窗口
    print("连接窗口...")
    main_window = connect_to_window(process)
    if not main_window:
        print("✗ 无法连接到窗口")
        return False
    
    # 测试截图
    print("进行截图测试...")
    test_screenshot_path = "test_screenshot.png"
    
    screenshot = capture_window_screenshot(main_window, test_screenshot_path)
    
    if screenshot is not None:
        print(f"✓ 截图成功!")
        print(f"  图像尺寸: {screenshot.shape}")
        print(f"  文件保存: {test_screenshot_path}")
        
        # 检查文件是否存在
        if os.path.exists(test_screenshot_path):
            file_size = os.path.getsize(test_screenshot_path)
            print(f"  文件大小: {file_size} 字节")
            return True
        else:
            print("✗ 截图文件未保存")
            return False
    else:
        print("✗ 截图失败")
        return False

def test_template_matching():
    """
    测试模板匹配功能（需要预先准备模板文件）
    """
    print("\n=== 测试模板匹配功能 ===")
    
    # 检查是否有测试截图
    if not os.path.exists("test_screenshot.png"):
        print("✗ 需要先运行截图测试")
        return False
    
    # 读取测试截图
    import cv2
    screenshot = cv2.imread("test_screenshot.png")
    if screenshot is None:
        print("✗ 无法读取测试截图")
        return False
    
    print("✓ 成功读取测试截图")
    
    # 创建一个简单的测试模板（从截图中裁剪一小块）
    print("创建测试模板...")
    height, width = screenshot.shape[:2]
    
    # 裁剪左上角的一小块作为测试模板
    template_size = 50
    test_template = screenshot[10:10+template_size, 10:10+template_size]
    
    # 保存测试模板
    test_template_path = "test_template.png"
    cv2.imwrite(test_template_path, test_template)
    print(f"✓ 测试模板已保存: {test_template_path}")
    
    # 进行模板匹配测试
    print("进行模板匹配测试...")
    result = find_template_in_screenshot(screenshot, test_template_path, threshold=0.9)
    
    if result['found']:
        print(f"✓ 模板匹配成功!")
        print(f"  置信度: {result['confidence']:.3f}")
        print(f"  位置: {result['location']}")
        print(f"  中心: {result['center']}")
        return True
    else:
        print(f"✗ 模板匹配失败 (最高置信度: {result['confidence']:.3f})")
        return False

def create_sample_templates():
    """
    创建示例模板文件的指导
    """
    print("\n=== 创建示例模板 ===")
    
    if not os.path.exists("test_screenshot.png"):
        print("请先运行截图测试生成 test_screenshot.png")
        return
    
    print("基于测试截图创建示例模板...")
    
    import cv2
    screenshot = cv2.imread("test_screenshot.png")
    if screenshot is None:
        print("无法读取测试截图")
        return
    
    height, width = screenshot.shape[:2]
    
    # 创建几个不同位置的模板示例
    templates = [
        ("top_left_template.png", (10, 10, 80, 80)),
        ("top_right_template.png", (width-90, 10, 80, 80)),
        ("center_template.png", (width//2-40, height//2-40, 80, 80)),
    ]
    
    for template_name, (x, y, w, h) in templates:
        if x + w <= width and y + h <= height:
            template_region = screenshot[y:y+h, x:x+w]
            cv2.imwrite(template_name, template_region)
            print(f"✓ 创建模板: {template_name} (位置: {x},{y}, 大小: {w}x{h})")
    
    print("示例模板创建完成!")
    print("你可以使用这些模板测试匹配功能")

def test_pyautogui_functions():
    """
    测试pyautogui模板匹配功能
    """
    print("\n=== 测试PyAutoGUI功能 ===")

    # 检查pyautogui是否可用
    if not check_pyautogui_dependencies():
        print("✗ PyAutoGUI功能不可用")
        return False

    # 检查是否有测试模板
    if not os.path.exists("top_left_template.png"):
        print("✗ 需要先创建示例模板")
        print("请先运行 '4. 创建示例模板'")
        return False

    print("测试PyAutoGUI模板匹配...")

    # 测试全屏搜索
    print("1. 测试全屏模板搜索...")
    result = locate_template_on_screen("top_left_template.png", confidence=0.7)

    if result['found']:
        print(f"✓ 全屏搜索成功! 位置: {result['center']}")
    else:
        print("✗ 全屏搜索未找到模板")

    # 测试窗口区域搜索
    print("2. 测试窗口区域搜索...")

    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        print("✗ 无法启动同花顺")
        return False

    main_window = connect_to_window(process)
    if not main_window:
        print("✗ 无法连接到窗口")
        return False

    # 在窗口中搜索
    result = locate_template_in_window(main_window, "top_left_template.png", confidence=0.7)

    if result['found']:
        print(f"✓ 窗口区域搜索成功! 位置: {result['center']}")

        # 测试点击功能
        print("3. 测试模板点击...")
        success = click_template_in_window(main_window, "top_left_template.png", confidence=0.7)

        if success:
            print("✓ 模板点击成功!")
            return True
        else:
            print("✗ 模板点击失败")
            return False
    else:
        print("✗ 窗口区域搜索未找到模板")
        return False

def test_wait_for_template():
    """
    测试等待模板出现功能
    """
    print("\n=== 测试等待模板功能 ===")

    if not check_pyautogui_dependencies():
        return False

    if not os.path.exists("top_left_template.png"):
        print("需要先创建示例模板")
        return False

    print("测试等待模板出现功能...")
    print("将在5秒内查找模板...")

    result = wait_for_template_on_screen("top_left_template.png", timeout=5, confidence=0.7)

    if result['found']:
        print(f"✓ 模板出现! 位置: {result['center']}")
        return True
    else:
        print("✗ 等待超时，未找到模板")
        return False

def compare_performance():
    """
    比较不同方法的性能
    """
    print("\n=== 性能比较测试 ===")

    if not os.path.exists("test_screenshot.png") or not os.path.exists("top_left_template.png"):
        print("需要先运行其他测试生成必要文件")
        return

    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        return

    main_window = connect_to_window(process)
    if not main_window:
        return

    template_path = "top_left_template.png"
    confidence = 0.7

    print(f"使用模板: {template_path}")
    print(f"置信度: {confidence}")
    print("-" * 40)

    # 测试OpenCV方法
    print("OpenCV方法:")
    start_time = time.time()
    screenshot = capture_window_screenshot(main_window)
    if screenshot is not None:
        result1 = find_template_in_screenshot(screenshot, template_path, confidence)
        time1 = time.time() - start_time
        print(f"  结果: {'找到' if result1['found'] else '未找到'}")
        print(f"  耗时: {time1:.3f}秒")

    # 测试PyAutoGUI方法
    if check_pyautogui_dependencies():
        print("PyAutoGUI全屏方法:")
        start_time = time.time()
        result2 = locate_template_on_screen(template_path, confidence)
        time2 = time.time() - start_time
        print(f"  结果: {'找到' if result2['found'] else '未找到'}")
        print(f"  耗时: {time2:.3f}秒")

        print("PyAutoGUI窗口方法:")
        start_time = time.time()
        result3 = locate_template_in_window(main_window, template_path, confidence)
        time3 = time.time() - start_time
        print(f"  结果: {'找到' if result3['found'] else '未找到'}")
        print(f"  耗时: {time3:.3f}秒")

def run_all_tests():
    """
    运行所有测试
    """
    print("开始运行所有测试...")
    print("=" * 50)

    # 测试依赖
    if not test_dependencies():
        print("依赖测试失败，请安装缺失的包")
        return

    # 测试截图
    if not test_screenshot_function():
        print("截图测试失败")
        return

    # 测试模板匹配
    if not test_template_matching():
        print("模板匹配测试失败")
        return

    # 创建示例模板
    create_sample_templates()

    # 测试PyAutoGUI功能
    if not test_pyautogui_functions():
        print("PyAutoGUI测试失败")
        return

    print("\n" + "=" * 50)
    print("✓ 所有测试通过!")
    print("你现在可以使用所有截图和模板匹配功能了")
    print("包括:")
    print("- OpenCV模板匹配")
    print("- PyAutoGUI模板匹配")
    print("- 窗口截图")
    print("- 自动点击")

def cleanup_test_files():
    """
    清理测试文件
    """
    test_files = [
        "test_screenshot.png",
        "test_template.png",
        "top_left_template.png",
        "top_right_template.png",
        "center_template.png"
    ]
    
    print("清理测试文件...")
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"删除: {file}")
    
    print("清理完成")

if __name__ == "__main__":
    print("同花顺截图和模板匹配功能测试")
    print("=" * 40)
    
    while True:
        print("\n请选择操作:")
        print("1. 检查依赖包")
        print("2. 测试截图功能")
        print("3. 测试模板匹配 (OpenCV)")
        print("4. 创建示例模板")
        print("5. 测试PyAutoGUI功能")
        print("6. 测试等待模板功能")
        print("7. 性能比较测试")
        print("8. 运行所有测试")
        print("9. 清理测试文件")
        print("10. 退出")

        choice = input("请输入选择 (1-10): ").strip()

        if choice == "1":
            test_dependencies()
        elif choice == "2":
            test_screenshot_function()
        elif choice == "3":
            test_template_matching()
        elif choice == "4":
            create_sample_templates()
        elif choice == "5":
            test_pyautogui_functions()
        elif choice == "6":
            test_wait_for_template()
        elif choice == "7":
            compare_performance()
        elif choice == "8":
            run_all_tests()
        elif choice == "9":
            cleanup_test_files()
        elif choice == "10":
            print("退出测试程序")
            break
        else:
            print("无效选择，请重新输入")
