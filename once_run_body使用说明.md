# once_run_body() 函数使用说明

## 函数概述

`once_run_body()` 函数是从 `task_main.py` 中提取出来的重复运行逻辑，用于处理单只股票的完整流程，包括可视化操作、截图和信号分析。

## 函数签名

```python
def once_run_body(ths_window, stock, wait_if_paused_func, should_halt_func, clog_func):
    """
    执行单次股票处理的主体逻辑
    
    Args:
        ths_window: 同花顺窗口对象
        stock: 股票信息列表 [代码, 名称, 时间周期]
        wait_if_paused_func: 暂停等待函数
        should_halt_func: 停止检查函数
        clog_func: 日志记录函数
    
    Returns:
        signal: 信号处理结果 [信号值, 状态信息]
    """
```

## 参数详解

### 1. ths_window
- **类型**: pywinauto窗口对象
- **说明**: 同花顺软件的主窗口对象，用于执行界面操作
- **获取方式**: 通过 `open_ths_pyauto.connect_to_window(process)` 获取

### 2. stock
- **类型**: list
- **格式**: `[股票代码, 股票名称, 时间周期]`
- **示例**: `["000001", "平安银行", "60min"]`
- **说明**: 
  - `stock[0]`: 股票代码（如 "000001"）
  - `stock[1]`: 股票名称（如 "平安银行"）
  - `stock[2]`: 时间周期（如 "60min"）

### 3. wait_if_paused_func
- **类型**: function
- **签名**: `def wait_if_paused() -> None`
- **说明**: 检查是否暂停，如果暂停则等待恢复
- **示例实现**:
```python
def wait_if_paused():
    if not control:
        return
    while control.is_paused and not control.should_stop:
        control.set_status("已暂停")
        time.sleep(0.2)
```

### 4. should_halt_func
- **类型**: function
- **签名**: `def should_halt() -> bool`
- **说明**: 检查是否应该停止执行
- **返回值**: True表示应该停止，False表示继续执行
- **示例实现**:
```python
def should_halt() -> bool:
    return bool(control and control.should_stop)
```

### 5. clog_func
- **类型**: function
- **签名**: `def clog(msg: str) -> None`
- **说明**: 记录日志信息
- **示例实现**:
```python
def clog(msg: str):
    try:
        if control:
            control.log(msg)
        else:
            log_info(msg)
    except Exception:
        log_error(f"日志记录异常: {msg}")
```

## 返回值

函数返回一个列表 `[信号值, 状态信息]`：
- `signal[0]`: 整数，信号值（0表示无信号，非0表示有信号）
- `signal[1]`: 字符串，状态信息或错误描述

## 函数执行流程

1. **可视化操作**: 调用 `sub_cycle_body.sub_cycle_body()` 执行股票的界面操作
2. **暂停检查**: 调用 `wait_if_paused_func()` 检查是否需要暂停
3. **停止检查**: 调用 `should_halt_func()` 检查是否需要停止
4. **窗口截图**: 使用 `open_ths_pyauto.capture_window_screenshot()` 截取窗口
5. **信号分析**: 调用 `gen_signal.deal_signal_60mins()` 分析截图中的信号
6. **返回结果**: 返回信号分析结果

## 在main()函数中的使用

```python
def main(args=None, control=None):
    # ... 初始化代码 ...
    
    # 定义回调函数
    def wait_if_paused():
        if not control:
            return
        while control.is_paused and not control.should_stop:
            control.set_status("已暂停")
            time.sleep(0.2)

    def should_halt() -> bool:
        return bool(control and control.should_stop)

    def clog(msg: str):
        try:
            if control:
                control.log(msg)
            else:
                log_info(msg)
        except Exception:
            log_error(f"日志记录异常: {msg}")
    
    # ... 获取ths_window和stock_list ...
    
    for index, stock_t in enumerate(stock_list):
        # 准备股票数据
        stock = [
            extract_stock_code(stock_t[0]),
            stock_t[1],
            "60min"
        ]
        
        # 调用once_run_body处理股票
        signal = once_run_body(ths_window, stock, wait_if_paused, should_halt, clog)
        
        # 如果第一次无信号，重试一次
        if signal[0] == 0:
            log_info(f"股票 {stock[0]} 第一次无信号，重试一次")
            time.sleep(1)
            signal = once_run_body(ths_window, stock, wait_if_paused, should_halt, clog)
        
        # 处理结果
        if signal[0] != 0:
            log_success(f"股票 {stock[0]} 检测到信号")
        else:
            log_info(f"股票 {stock[0]} 无信号")
```

## 错误处理

函数内部包含完整的错误处理：

1. **截图失败**: 如果截图失败，返回 `[0, "截图失败"]`
2. **文件不存在**: 如果配置文件不存在，返回相应错误信息
3. **信号处理异常**: 如果信号分析出错，返回错误信息
4. **其他异常**: 捕获所有异常并返回错误描述

## 日志输出

函数会输出详细的日志信息：
- 执行可视化操作的进度
- 截图成功/失败状态
- 信号分析结果
- 错误信息

## 注意事项

1. **窗口对象**: 确保 `ths_window` 是有效的窗口对象
2. **股票数据**: 确保 `stock` 列表包含3个元素且格式正确
3. **回调函数**: 确保所有回调函数都正确实现
4. **资源清理**: 函数会自动清理临时截图文件
5. **线程安全**: 函数设计为在单线程环境中使用

## 性能优化

- 使用内存中的图像处理，减少磁盘I/O
- 自动清理临时文件，避免磁盘空间浪费
- 详细的错误信息，便于调试和监控

## 扩展建议

1. **批量处理**: 可以扩展为批量处理多只股票
2. **并行处理**: 可以考虑并行处理多只股票（需要注意窗口操作的线程安全）
3. **缓存机制**: 可以添加结果缓存，避免重复处理
4. **配置化**: 可以将更多参数配置化，提高灵活性
