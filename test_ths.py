"""
测试同花顺软件打开脚本
"""
from open_ths_pyauto import open_tonghuashun_complete
import pyautogui

def test_open_ths():
    """
    测试打开同花顺软件
    """
    print("开始测试同花顺软件打开...")
    print("注意：请确保同花顺软件路径正确：C:\\同花顺软件\\同花顺金融大师\\hexin.exe")
    print("如果路径不正确，请修改 open_ths_pyauto.py 中的 ths_path 变量")

    # 设置pyautogui安全设置
    pyautogui.FAILSAFE = True

    # 执行测试
    window = open_tonghuashun_complete()

    if window:
        print("✓ 测试成功！同花顺软件已打开并完成操作")
        return True
    else:
        print("✗ 测试失败！")
        return False

def test_individual_functions():
    """
    测试各个独立功能
    """
    from open_ths_pyauto import (
        launch_tonghuashun_process,
        connect_to_window,
        resize_and_move_window,
        input_text_to_window,
        press_enter
    )

    print("=== 分步测试各个功能 ===")

    # 测试1：启动进程
    print("1. 测试启动进程...")
    process = launch_tonghuashun_process()
    if not process:
        print("✗ 启动进程失败")
        return False
    print("✓ 启动进程成功")

    # 测试2：连接窗口
    print("2. 测试连接窗口...")
    window = connect_to_window(process)
    if not window:
        print("✗ 连接窗口失败")
        return False
    print("✓ 连接窗口成功")

    # 测试3：调整窗口
    print("3. 测试调整窗口...")
    resize_success = resize_and_move_window(window)
    print(f"✓ 调整窗口{'成功' if resize_success else '部分成功'}")

    # 测试4：输入文本
    print("4. 测试输入文本...")
    input_success = input_text_to_window(window, '35')
    print(f"✓ 输入文本{'成功' if input_success else '失败'}")

    # 测试5：按回车
    print("5. 测试按回车...")
    enter_success = press_enter()
    print(f"✓ 按回车{'成功' if enter_success else '失败'}")

    print("=== 分步测试完成 ===")
    return True

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整测试")
    print("2. 分步测试")

    choice = input("请选择 (1 或 2): ").strip()

    if choice == "1":
        test_open_ths()
    elif choice == "2":
        test_individual_functions()
    else:
        print("使用默认完整测试...")
        test_open_ths()
