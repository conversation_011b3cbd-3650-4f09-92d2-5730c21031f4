# 同花顺窗口截图和模板匹配功能

本文档介绍如何使用新添加的窗口截图和图像模板匹配功能。

## 功能概述

新增了以下功能：
1. **窗口截图** - 对激活的同花顺窗口进行截图
2. **模板匹配** - 在截图中查找指定的图像模板
3. **模板点击** - 自动点击找到的模板位置

## 安装依赖

首先安装所需的Python包：

```bash
pip install -r requirements.txt
```

主要新增依赖：
- `opencv-python` - 图像处理和模板匹配
- `numpy` - 数值计算
- `Pillow` - 图像处理

## 核心函数

### 1. 窗口截图

```python
def capture_window_screenshot(window, save_path=None):
    """
    对指定窗口进行截图
    
    Args:
        window: pywinauto窗口对象
        save_path (str): 保存截图的路径，如果为None则不保存
    
    Returns:
        numpy.ndarray: 截图的numpy数组，如果失败返回None
    """
```

**使用示例：**
```python
# 截图并保存
screenshot = capture_window_screenshot(main_window, "screenshot.png")

# 只截图不保存
screenshot = capture_window_screenshot(main_window)
```

### 2. 模板匹配

```python
def find_template_in_screenshot(screenshot, template_path, threshold=0.8):
    """
    在截图中查找模板图像
    
    Args:
        screenshot: 截图的numpy数组
        template_path (str): 模板图像文件路径
        threshold (float): 匹配阈值，默认0.8
    
    Returns:
        dict: 包含匹配结果的字典
    """
```

**返回结果格式：**
```python
{
    'found': bool,          # 是否找到
    'confidence': float,    # 最高匹配度
    'location': tuple,      # 匹配位置 (x, y)
    'center': tuple,        # 匹配中心点 (x, y)
    'rectangle': tuple      # 匹配矩形 (x, y, w, h)
}
```

### 3. 模板点击

```python
def click_template_if_found(window, template_path, threshold=0.8, click_offset=(0, 0)):
    """
    在窗口中查找模板并点击
    
    Args:
        window: pywinauto窗口对象
        template_path (str): 模板图像文件路径
        threshold (float): 匹配阈值
        click_offset (tuple): 点击偏移量 (x_offset, y_offset)
    
    Returns:
        bool: 是否成功找到并点击
    """
```

## 使用步骤

### 步骤1：创建模板图像

1. 运行程序对同花顺窗口截图：
```python
screenshot = capture_window_screenshot(main_window, "full_screenshot.png")
```

2. 使用图像编辑软件（如画图、Photoshop等）打开截图

3. 裁剪出你想要匹配的区域，例如：
   - 搜索框
   - 买入按钮
   - 卖出按钮
   - 菜单图标

4. 保存为PNG格式，使用描述性文件名：
   - `search_box_template.png`
   - `buy_button_template.png`
   - `sell_button_template.png`

### 步骤2：测试模板匹配

```python
# 截图
screenshot = capture_window_screenshot(main_window)

# 查找模板
result = find_template_in_screenshot(screenshot, "search_box_template.png", threshold=0.7)

if result['found']:
    print(f"找到搜索框! 置信度: {result['confidence']:.3f}")
    print(f"位置: {result['center']}")
else:
    print("未找到搜索框")
```

### 步骤3：自动点击

```python
# 查找并点击搜索框
success = click_template_if_found(main_window, "search_box_template.png", threshold=0.7)

if success:
    # 点击成功后输入内容
    pywinauto.keyboard.send_keys("000001{ENTER}")
    print("已输入股票代码")
```

## 完整使用示例

```python
from open_ths_pyauto import *

def main():
    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        return
    
    # 连接窗口
    main_window = connect_to_window(process)
    if not main_window:
        return
    
    # 截图
    screenshot = capture_window_screenshot(main_window, "current_window.png")
    print("窗口截图完成")
    
    # 查找并点击搜索框
    if click_template_if_found(main_window, "search_box_template.png"):
        # 输入股票代码
        pywinauto.keyboard.send_keys("000001{ENTER}")
        print("搜索股票成功")
        
        # 等待加载后再次截图
        time.sleep(2)
        capture_window_screenshot(main_window, "search_result.png")
        print("搜索结果截图完成")

if __name__ == "__main__":
    main()
```

## 演示程序

运行 `screenshot_template_demo.py` 可以体验各种功能：

```bash
python screenshot_template_demo.py
```

该程序提供了交互式菜单，包括：
- 简单截图演示
- 模板匹配演示
- 模板创建指导
- 交互式操作

## 注意事项

1. **模板质量**：
   - 选择独特的、不容易变化的界面元素
   - 避免包含可能变化的文字内容
   - 模板大小建议在 50x50 到 200x200 像素之间

2. **匹配阈值**：
   - 默认阈值是 0.8（80%匹配度）
   - 如果匹配不到，可以降低阈值到 0.6-0.7
   - 如果误匹配太多，可以提高阈值到 0.85-0.9

3. **窗口状态**：
   - 确保同花顺窗口在前台且完全可见
   - 窗口大小和位置变化可能影响模板匹配

4. **性能考虑**：
   - 截图和模板匹配需要一定时间
   - 建议在操作间添加适当的等待时间

## 故障排除

**问题1：模板匹配失败**
- 检查模板文件是否存在
- 降低匹配阈值
- 重新制作更清晰的模板

**问题2：点击位置不准确**
- 使用 `click_offset` 参数调整点击位置
- 检查窗口是否完全可见

**问题3：截图失败**
- 确保窗口在前台
- 检查窗口句柄是否有效
- 重新连接窗口
