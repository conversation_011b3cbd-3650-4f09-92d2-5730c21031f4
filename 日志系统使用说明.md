# 项目日志系统使用说明

## 概述

本项目已经整理并统一了日志系统，所有的日志信息现在都可以在Web终端中实时显示。系统包含以下主要组件：

1. **统一日志管理器** (`logger_manager.py`) - 核心日志管理系统
2. **Web控制台** (`app.py` + `templates/index.html`) - 可视化日志显示界面
3. **日志捕获功能** - 自动捕获所有print语句和日志输出

## 主要功能

### 1. 统一日志管理
- 所有模块的日志输出都通过统一的日志管理器处理
- 支持多种日志级别：INFO、WARNING、ERROR、SUCCESS、PROGRESS
- 自动添加时间戳和日志级别标识
- 支持日志回调和队列管理

### 2. Web终端显示
- 实时显示所有日志信息
- 根据日志级别使用不同颜色和样式
- 支持自动滚动到最新日志
- 提供日志清空、导出等功能

### 3. 任务控制
- 支持任务的启动、暂停、恢复、停止
- 实时显示任务进度和状态
- 支持模拟模式测试

## 使用方法

### 启动Web控制台

```bash
python app.py
```

然后在浏览器中访问：http://localhost:5000

### 在代码中使用日志系统

#### 方法1：使用便捷函数
```python
from logger_manager import log_info, log_warning, log_error, log_success, log_progress

log_info("这是一条信息日志")
log_warning("这是一条警告日志")
log_error("这是一条错误日志")
log_success("这是一条成功日志")
log_progress("这是一条进度日志")
```

#### 方法2：使用日志管理器
```python
from logger_manager import get_logger

logger = get_logger()
logger.log("自定义消息", "INFO")
```

#### 方法3：使用上下文管理器捕获print语句
```python
from logger_manager import LogCaptureContext

with LogCaptureContext():
    print("这条print语句会被自动捕获到日志系统")
```

### 日志级别说明

- **INFO** (蓝色) - 一般信息
- **SUCCESS** (绿色) - 成功操作
- **WARNING** (黄色) - 警告信息
- **ERROR** (红色) - 错误信息
- **PROGRESS** (青色) - 进度信息

## Web界面功能

### 状态面板
- 显示当前任务运行状态
- 显示任务进度百分比
- 实时更新状态信息

### 控制按钮
- **开始任务** - 启动主任务
- **暂停** - 暂停当前任务
- **恢复** - 恢复暂停的任务
- **停止** - 停止当前任务

### 日志面板
- **实时显示** - 自动显示新的日志信息
- **颜色分类** - 根据日志级别显示不同颜色
- **清空日志** - 清空当前显示的日志
- **自动滚动** - 开关自动滚动到最新日志
- **导出日志** - 将日志导出为文本文件

## 文件结构

```
ths_autowin/
├── logger_manager.py          # 统一日志管理器
├── app.py                     # Flask Web应用
├── templates/
│   └── index.html            # Web界面模板
├── task_main.py              # 主任务文件（已更新使用新日志系统）
├── test_logger.py            # 日志系统测试文件
└── 日志系统使用说明.md        # 本说明文件
```

## 测试日志系统

运行测试脚本验证日志系统功能：

```bash
python test_logger.py
```

## 已更新的模块

以下模块已经更新为使用新的日志系统：

1. **task_main.py** - 主任务模块
2. **app.py** - Web应用模块

其他模块（如`gen_signal.py`、`open_ths_pyauto.py`等）中的print语句在任务运行时会被自动捕获到日志系统中。

## 注意事项

1. **日志捕获** - 当任务运行时，所有print语句都会被自动捕获并显示在Web终端中
2. **性能考虑** - 日志系统使用队列管理，不会阻塞主任务执行
3. **线程安全** - 日志管理器是线程安全的，支持多线程环境
4. **内存管理** - 日志队列有大小限制，避免内存溢出

## 故障排除

### 如果Web界面无法访问
1. 检查Flask应用是否正常启动
2. 确认端口5000没有被其他程序占用
3. 检查防火墙设置

### 如果日志不显示
1. 确认日志管理器已正确初始化
2. 检查日志回调函数是否正确设置
3. 查看浏览器控制台是否有JavaScript错误

### 如果任务无法启动
1. 检查task_main.py中的依赖是否正确安装
2. 确认数据库连接配置是否正确
3. 查看日志中的错误信息

## 扩展功能

可以根据需要添加以下功能：

1. **日志过滤** - 按级别或关键词过滤日志
2. **日志搜索** - 在历史日志中搜索特定内容
3. **日志持久化** - 将日志保存到文件或数据库
4. **邮件通知** - 在出现错误时发送邮件通知
5. **性能监控** - 添加系统性能监控日志

## 联系信息

如有问题或建议，请联系开发团队。
