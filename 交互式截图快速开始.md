# 交互式屏幕截图功能 - 快速开始指南

## 🚀 快速开始

### 1. 检查依赖
确保已安装所需的依赖库：
```bash
pip install keyboard pyautogui Pillow opencv-python numpy
```

### 2. 运行程序
有三种方式启动交互式截图功能：

#### 方式一：运行主程序（推荐）
```bash
python windows_precessing.py
```
选择菜单选项：
- `1` - 启动全局快捷键监听模式
- `2` - 立即执行交互式截图

#### 方式二：运行演示程序
```bash
python demo_interactive_screenshot.py
```

#### 方式三：运行测试程序
```bash
python test_interactive_screenshot.py
```

## 📋 使用步骤

### 全局快捷键模式
1. 启动程序并选择快捷键监听模式
2. 程序在后台运行
3. 按 `Ctrl+Shift+S` 触发截图
4. 在全屏覆盖层上拖拽选择区域
5. 按 `回车` 确认选择
6. 自动截图并进行K线选择
7. 按 `Ctrl+C` 退出程序

### 直接调用模式
1. 启动程序并选择立即执行模式
2. 在全屏覆盖层上拖拽选择区域
3. 按 `回车` 确认选择
4. 自动截图并进行K线选择

## 🎮 操作说明

### 区域选择
- **拖拽**: 鼠标左键按住拖拽选择矩形区域
- **预览**: 实时显示红色选择框
- **确认**: 按 `回车键` 确认选择
- **取消**: 按 `ESC键` 取消选择

### K线中心点选择
- 截图完成后会自动打开截图文件
- 在弹出的窗口中点击K线的中心点
- 建议选择15个K线的中心点
- 完成选择后按任意键或等待自动完成
- 按 `ESC键` 可以取消选择

## 📁 文件输出

### 截图文件
- **位置**: `resources/images/`
- **命名**: `interactive_screenshot_YYYYMMDD_HHMMSS.png`
- **格式**: PNG

### 配置文件
程序会自动更新 `resources/coor_config.json`，添加以下信息：
```json
{
    "interactive_capture": {
        "last_screenshot_path": "截图文件路径",
        "original_selection": {
            "p1": [x1, y1],
            "p2": [x2, y2]
        },
        "kline_centers": [x1, x2, x3, ...],
        "kline_count": 15,
        "timestamp": "2025-08-23 22:30:00"
    },
    "line_x": [x1, x2, x3, ...],
    "line_width": 32,
    "lines_range": [[x1-width, x1+width], [x2-width, x2+width], ...]
}
```

## ⚠️ 注意事项

### 权限要求
- 全局快捷键功能可能需要**管理员权限**
- 如果快捷键不工作，请右键选择"以管理员身份运行"

### 系统兼容性
- 支持 Windows 系统
- 支持多显示器环境
- 自动适配不同屏幕分辨率

### 常见问题
1. **快捷键不响应**: 以管理员权限运行程序
2. **覆盖层不显示**: 检查 tkinter 安装，重启程序
3. **截图失败**: 检查磁盘空间和目录权限
4. **K线选择失败**: 确认 opencv-python 已安装

## 🔧 故障排除

### 检查依赖
```bash
python test_interactive_screenshot.py
```
程序会自动检查所有依赖库的安装状态。

### 测试功能
```bash
python demo_interactive_screenshot.py
```
运行演示程序测试各项功能。

### 查看日志
程序运行时会在控制台输出详细的操作日志，包括：
- 坐标选择信息
- 截图保存路径
- 配置文件更新状态
- 错误信息（如果有）

## 📞 技术支持

如果遇到问题，请检查：
1. 所有依赖库是否正确安装
2. 是否以管理员权限运行（快捷键模式）
3. 磁盘空间是否充足
4. 防火墙或安全软件是否阻止程序运行

## 🎯 使用技巧

1. **精确选择**: 拖拽时可以看到实时坐标信息，帮助精确选择区域
2. **快速重试**: 如果选择错误，按ESC取消后可以重新选择
3. **批量处理**: 快捷键模式下可以连续进行多次截图
4. **配置复用**: 程序会记住上次的选择，便于重复操作

---

**开始使用交互式屏幕截图功能，让K线分析更加高效！** 🚀
