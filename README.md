# 同花顺自动化操作脚本

这个项目提供了使用 pyautogui 和 pywinauto 自动打开同花顺软件并执行操作的完整解决方案。

## 功能特性

- ✅ 自动启动同花顺软件
- ✅ 获取窗口句柄
- ✅ 调整窗口大小为 1920x1080
- ✅ 移动窗口到屏幕左上角 (0,0)
- ✅ 在窗口中输入 '35'
- ✅ 按回车键执行操作
- ✅ 多重输入方法确保成功率
- ✅ 完善的错误处理和重试机制

## 文件说明

### 核心文件
- `open_ths_pyauto.py` - 主要功能模块，包含所有核心函数
- `requirements.txt` - 依赖包列表

### 测试文件
- `final_test.py` - 最终测试脚本（推荐使用）
- `test_ths.py` - 基础测试脚本
- `debug_input.py` - 键盘输入调试脚本

### 文档文件
- `keyboard_input_solutions.md` - 键盘输入问题解决方案
- `README.md` - 本说明文件

## 安装依赖

```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install pyautogui pywinauto pywin32 pyperclip
```

## 使用方法

### 方法1：运行最终测试脚本（推荐）
```bash
python final_test.py
```

### 方法2：直接运行主脚本
```bash
python open_ths_pyauto.py
```

### 方法3：在代码中调用
```python
from open_ths_pyauto import open_tonghuashun_complete

# 执行完整操作
window = open_tonghuashun_complete()
if window:
    print("操作成功")
else:
    print("操作失败")
```

### 方法4：调用单独的函数
```python
from open_ths_pyauto import (
    launch_tonghuashun_process,
    connect_to_window,
    resize_and_move_window,
    input_text_to_window,
    press_enter
)

# 分步执行
process = launch_tonghuashun_process()
window = connect_to_window(process)
resize_and_move_window(window, 0, 0, 1920, 1080)
input_text_to_window(window, '35')
press_enter()
```

## 核心函数说明

### `launch_tonghuashun_process(ths_path)`
启动同花顺软件进程
- 参数：软件路径（默认：`C:\同花顺软件\同花顺金融大师\hexin.exe`）
- 返回：进程对象或None

### `connect_to_window(process, max_retries, wait_time)`
连接到同花顺窗口
- 参数：进程对象、最大重试次数、等待时间
- 返回：窗口对象或None

### `resize_and_move_window(window, x, y, width, height)`
调整窗口大小和位置
- 参数：窗口对象、坐标和尺寸
- 返回：操作是否成功

### `input_text_to_window(window, text)`
向窗口输入文本（增强版，多种输入方法）
- 参数：窗口对象、要输入的文本
- 返回：输入是否成功

### `press_enter()`
按回车键
- 返回：操作是否成功

## 注意事项

1. **软件路径**：请确保同花顺软件安装在默认路径，或修改脚本中的路径
2. **权限**：可能需要管理员权限来控制其他应用程序
3. **输入法**：建议使用英文输入法状态
4. **干扰**：运行时请不要操作鼠标键盘，避免干扰自动化操作
5. **安全**：pyautogui的FAILSAFE功能已启用，紧急情况下将鼠标移到屏幕左上角可停止

## 故障排除

### 键盘输入失败
1. 检查窗口是否正确获得焦点
2. 确认输入法为英文状态
3. 尝试手动输入验证窗口可用性
4. 运行 `debug_input.py` 进行详细调试

### 窗口连接失败
1. 确认同花顺软件路径正确
2. 检查软件是否正常启动
3. 尝试增加等待时间
4. 检查是否有权限问题

### 窗口调整失败
1. 确认屏幕分辨率支持1920x1080
2. 检查是否有多显示器干扰
3. 尝试手动调整窗口验证

## 自定义配置

如果需要修改默认设置，可以在调用函数时传入参数：

```python
# 自定义软件路径
window = open_tonghuashun_complete(r"D:\MyPath\hexin.exe")

# 自定义窗口大小和位置
resize_and_move_window(window, 100, 100, 1600, 900)

# 自定义输入内容
input_text_to_window(window, "000001")
```
