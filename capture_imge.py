# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import pyautogui
from PIL import Image, ImageTk, ImageDraw
import cv2
import numpy as np
from datetime import datetime
import os
import threading
import time
from typing import Tuple, Optional, Callable
import pygetwindow as gw
from datetime import datetime

class ScreenshotTool:
    def __init__(self):
        """初始化截图工具"""
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False

        # 创建保存目录
        self.save_directory = "screenshots"
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)

    def capture_region(self, x: int, y: int, width: int, height: int,
                      save_path: Optional[str] = None) -> Image.Image:
        """
        截取指定区域

        Args:
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            save_path: 保存路径（可选）

        Returns:
            PIL Image对象
        """
        try:
            # 使用pyautogui截图
            screenshot = pyautogui.screenshot(region=(x, y, width, height))

            # 保存截图
            if save_path:
                screenshot.save(save_path)
                print(f"截图已保存到: {save_path}")

            return screenshot

        except Exception as e:
            print(f"截图失败: {e}")
            return None

    def capture_full_screen(self, save_path: Optional[str] = None) -> Image.Image:
        """
        全屏截图

        Args:
            save_path: 保存路径（可选）

        Returns:
            PIL Image对象
        """
        try:
            screenshot = pyautogui.screenshot()

            if save_path:
                screenshot.save(save_path)
                print(f"全屏截图已保存到: {save_path}")

            return screenshot

        except Exception as e:
            print(f"全屏截图失败: {e}")
            return None

    def capture_window(self, window_title: str, save_path: Optional[str] = None) -> Image.Image:
        """
        截取指定窗口

        Args:
            window_title: 窗口标题（支持模糊匹配）
            save_path: 保存路径（可选）

        Returns:
            PIL Image对象
        """
        try:
            # 查找窗口
            windows = gw.getWindowsWithTitle(window_title)
            if not windows:
                # 尝试模糊匹配
                all_windows = gw.getAllWindows()
                windows = [w for w in all_windows if window_title.lower() in w.title.lower()]

            if not windows:
                print(f"未找到窗口: {window_title}")
                return None

            window = windows[0]

            # 激活窗口
            if window.isMinimized:
                window.restore()
            window.activate()
            time.sleep(0.1)

            # 截取窗口区域
            screenshot = pyautogui.screenshot(region=(
                window.left, window.top, window.width, window.height
            ))

            if save_path:
                screenshot.save(save_path)
                print(f"窗口截图已保存到: {save_path}")

            return screenshot

        except Exception as e:
            print(f"窗口截图失败: {e}")
            return None

    def generate_filename(self, prefix: str = "screenshot", extension: str = "png") -> str:
        """
        生成带时间戳的文件名

        Args:
            prefix: 文件名前缀
            extension: 文件扩展名

        Returns:
            完整的文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{timestamp}.{extension}"
        return os.path.join(self.save_directory, filename)

    def delayed_screenshot(self, delay: int, x: int, y: int, width: int, height: int,
                          save_path: Optional[str] = None) -> Image.Image:
        """
        延时截图

        Args:
            delay: 延时秒数
            x, y, width, height: 截图区域
            save_path: 保存路径

        Returns:
            PIL Image对象
        """
        print(f"将在 {delay} 秒后截图...")
        time.sleep(delay)
        return self.capture_region(x, y, width, height, save_path)

    def capture_multiple_regions(self, regions: list, save_directory: Optional[str] = None) -> list:
        """
        批量截取多个区域

        Args:
            regions: 区域列表，每个元素为 (x, y, width, height, name)
            save_directory: 保存目录

        Returns:
            截图对象列表
        """
        screenshots = []
        save_dir = save_directory or self.save_directory

        for i, region in enumerate(regions):
            if len(region) == 5:
                x, y, width, height, name = region
                save_path = os.path.join(save_dir, f"{name}.png")
            else:
                x, y, width, height = region
                save_path = os.path.join(save_dir, f"region_{i+1}.png")

            screenshot = self.capture_region(x, y, width, height, save_path)
            screenshots.append(screenshot)
            time.sleep(0.1)  # 短暂延时避免过快截图

        return screenshots

# 简单使用函数
def quick_screenshot(x: int, y: int, width: int, height: int, filename: str = None):
    """
    快速截图函数

    Args:
        x, y: 左上角坐标
        width, height: 宽度和高度
        filename: 保存文件名（可选）
    """
    tool = ScreenshotTool()
    if filename is None:
        filename = tool.generate_filename()
    tool.capture_region(x, y, width, height, filename)
    return filename


def get_timestamp_str():
    """
    获取当前时间，格式为：YYYYMMDD_HHMMSS
    用于文件命名
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

# 使用示例
def capture_img(stock_name,var_period):
    # 方法1: 直接使用类
    tool = ScreenshotTool()
    # 截取指定区域 (x=100, y=100, width=800, height=600)
    # save_path = tool.generate_filename("test_region")
    p_path = rf"C:\Users\<USER>\Documents\ths_gs_resource\img\{var_period}"
    filename = f"{stock_name}_screenshot_{get_timestamp_str()}.png"
    save_path = os.path.join(p_path, filename)
    tool.capture_region(741, 120, 563, 421, save_path)
    print(f"截图已保存到: {save_path}")

def capture_days_img(args, var_list):
    """捕获日线图像"""
    tool = ScreenshotTool()

    # 获取当前脚本的目录并构建路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    p_path = os.path.join(current_dir, "resources", "cache", "img", "60min")

    # 确保目录存在
    os.makedirs(p_path, exist_ok=True)

    filename = f"{args.stock_name}_screenshot_{get_timestamp_str()}.png"
    save_path = os.path.join(p_path, filename)

    # 截取指定区域
    result = tool.capture_region(739, 162, 890, 515, save_path)
    if result:
        print(f"截图已保存到: {save_path}")
        return save_path
    else:
        print(f"截图失败")
        return None

def get_img_save_path(stock_name, var_period):
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建相对路径
    p_path = os.path.join(current_dir, "resources", "cache", "img", var_period)

    # 确保目录存在
    os.makedirs(p_path, exist_ok=True)

    filename = f"{stock_name}_screenshot_{get_timestamp_str()}.png"
    full_path = os.path.join(p_path, filename)
    save_path = [p_path, filename, full_path]
    return save_path

def resize_img_fullimg(image_path, point=None):
    if point == None:
        p0 = (136, 162)  # 左上角坐标
        p1 = (1629, 677)  # 右下角坐标
    else:
        p0 = point[0]
        p1 = point[1]

    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在: {image_path}")
        return None

    # 读取图片，使用绝对路径并处理中文路径
    try:
        # 使用cv2.imdecode处理中文路径问题
        image_path_abs = os.path.abspath(image_path)
        with open(image_path_abs, 'rb') as f:
            image_data = f.read()
        image_array = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

        if image is None:
            print(f"无法解码图片: {image_path}")
            return None

    except Exception as e:
        print(f"读取图片时发生错误: {e}")
        print(f"图片路径: {image_path}")
        return None

    # 设置裁剪区域 (左上角x, 左上角y, 右下角x, 右下角y)
    x1, y1 = p0[0], p0[1]  # 左上角
    x2, y2 = p1[0], p1[1]  # 右下角

    # 检查裁剪区域是否有效
    if x1 >= x2 or y1 >= y2:
        print(f"错误: 无效的裁剪区域: ({x1},{y1}) 到 ({x2},{y2})")
        return None

    if x2 > image.shape[1] or y2 > image.shape[0]:
        print(f"警告: 裁剪区域超出图片范围")
        print(f"图片尺寸: {image.shape[1]}x{image.shape[0]}")
        print(f"裁剪区域: ({x1},{y1}) 到 ({x2},{y2})")
        # 调整裁剪区域
        x2 = min(x2, image.shape[1])
        y2 = min(y2, image.shape[0])

    # 裁剪图片
    cropped = image[y1:y2, x1:x2]

    # 生成输出路径
    dir_path, file_name = os.path.split(image_path)
    cropped_path = os.path.join(dir_path, "cropped_kline_" + file_name)

    # 保存裁剪后的图片
    try:
        success = cv2.imwrite(cropped_path, cropped)
        if not success:
            print(f"保存裁剪图片失败: {cropped_path}")
            return None
        print(f"裁剪图片已保存: {cropped_path}")
        return cropped_path
    except Exception as e:
        print(f"保存裁剪图片时发生错误: {e}")
        return None

def resize_img_fullimg_array(image_array, point=None):
    """
    直接处理图像数组，避免文件读写

    Args:
        image_array: numpy数组格式的图像
        point: 裁剪区域坐标 [(x1,y1), (x2,y2)]

    Returns:
        裁剪后的图像数组
    """
    if point is None:
        p0 = (136, 162)  # 左上角坐标
        p1 = (1629, 677)  # 右下角坐标
    else:
        p0 = point[0]
        p1 = point[1]

    if image_array is None:
        print("错误: 输入图像数组为空")
        return None

    # 设置裁剪区域
    x1, y1 = p0[0], p0[1]  # 左上角
    x2, y2 = p1[0], p1[1]  # 右下角

    # 检查裁剪区域是否有效
    if x1 >= x2 or y1 >= y2:
        print(f"错误: 无效的裁剪区域: ({x1},{y1}) 到 ({x2},{y2})")
        return None

    if x2 > image_array.shape[1] or y2 > image_array.shape[0]:
        print(f"警告: 裁剪区域超出图片范围")
        print(f"图片尺寸: {image_array.shape[1]}x{image_array.shape[0]}")
        print(f"裁剪区域: ({x1},{y1}) 到 ({x2},{y2})")
        # 调整裁剪区域
        x2 = min(x2, image_array.shape[1])
        y2 = min(y2, image_array.shape[0])

    # 裁剪图片
    cropped = image_array[y1:y2, x1:x2]
    return cropped

# 基于内存（数组）的裁剪，避免磁盘IO
def resize_img_fullimg_array(image: np.ndarray, point=None) -> np.ndarray:
    """
    对给定的 OpenCV 图像数组进行裁剪，返回裁剪后的数组。
    Args:
        image: OpenCV 格式(BGR)的图像数组
        point: [(x1, y1), (x2, y2)] 左上角与右下角坐标
    Returns:
        裁剪后的 OpenCV 图像数组
    """
    if image is None:
        raise ValueError("输入图像为空")
    if point is None:
        p0 = (136, 162)
        p1 = (1629, 677)
    else:
        p0 = point[0]
        p1 = point[1]
    x1, y1 = p0[0], p0[1]
    x2, y2 = p1[0], p1[1]
    cropped = image[y1:y2, x1:x2]
    return cropped

# PIL 转 OpenCV (BGR)
def pil_to_cv2(img_pil) -> np.ndarray:
    """将 PIL.Image 转为 OpenCV BGR 数组"""
    if img_pil is None:
        return None
    # PIL 是 RGB，先转成 numpy，再转 BGR
    arr = np.array(img_pil)
    if arr.ndim == 2:
        return arr
    if arr.shape[2] == 3:
        return cv2.cvtColor(arr, cv2.COLOR_RGB2BGR)
    if arr.shape[2] == 4:
        return cv2.cvtColor(arr, cv2.COLOR_RGBA2BGR)
    return arr


def main(args):
    pass


if __name__ == '__main__':
    capture_img()


