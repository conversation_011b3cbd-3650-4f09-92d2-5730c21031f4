# test_once_run_body.py - 测试 once_run_body 函数
import time
from logger_manager import log_info, log_error, log_success, log_warning, log_progress
from task_main import once_run_body

def mock_wait_if_paused():
    """模拟暂停等待函数"""
    # 在测试中不需要实际暂停
    pass

def mock_should_halt():
    """模拟停止检查函数"""
    # 在测试中不停止
    return False

def mock_clog(msg):
    """模拟日志记录函数"""
    log_info(f"[MOCK_CLOG] {msg}")

def test_once_run_body_parameters():
    """测试 once_run_body 函数的参数传递"""
    log_info("=== 测试 once_run_body 函数参数传递 ===")
    
    # 模拟股票数据
    mock_stock = ["000001", "平安银行", "60min"]
    
    # 模拟窗口对象（在实际测试中这会是None，但函数应该能处理）
    mock_ths_window = None
    
    try:
        # 调用函数（预期会因为窗口为None而失败，但不应该有参数错误）
        result = once_run_body(
            ths_window=mock_ths_window,
            stock=mock_stock,
            wait_if_paused_func=mock_wait_if_paused,
            should_halt_func=mock_should_halt,
            clog_func=mock_clog
        )
        
        log_info(f"函数调用成功，返回结果: {result}")
        
        # 检查返回值格式
        if isinstance(result, list) and len(result) >= 2:
            log_success("返回值格式正确")
            log_info(f"信号值: {result[0]}")
            log_info(f"状态信息: {result[1]}")
        else:
            log_warning(f"返回值格式异常: {result}")
            
    except Exception as e:
        log_error(f"函数调用异常: {e}")
        # 这是预期的，因为没有真实的窗口对象

def test_function_signature():
    """测试函数签名是否正确"""
    log_info("=== 测试函数签名 ===")
    
    import inspect
    
    try:
        # 获取函数签名
        sig = inspect.signature(once_run_body)
        params = list(sig.parameters.keys())
        
        log_info(f"函数参数: {params}")
        
        expected_params = ['ths_window', 'stock', 'wait_if_paused_func', 'should_halt_func', 'clog_func']
        
        if params == expected_params:
            log_success("函数签名正确")
        else:
            log_error(f"函数签名不匹配，期望: {expected_params}, 实际: {params}")
            
        # 检查参数是否都是必需的
        for param_name, param in sig.parameters.items():
            if param.default == inspect.Parameter.empty:
                log_info(f"参数 {param_name} 是必需的")
            else:
                log_info(f"参数 {param_name} 有默认值: {param.default}")
                
    except Exception as e:
        log_error(f"检查函数签名时发生错误: {e}")

def test_stock_data_format():
    """测试股票数据格式"""
    log_info("=== 测试股票数据格式 ===")
    
    # 测试不同格式的股票数据
    test_stocks = [
        ["000001", "平安银行", "60min"],
        ["000002", "万科A", "60min"],
        ["SZ000001", "平安银行", "60min"],  # 带前缀的代码
        ["600036", "招商银行", "60min"],
    ]
    
    for stock in test_stocks:
        log_info(f"测试股票数据: {stock}")
        
        # 检查数据格式
        if len(stock) == 3:
            log_success(f"股票 {stock[0]} 数据格式正确")
            log_info(f"  代码: {stock[0]}")
            log_info(f"  名称: {stock[1]}")
            log_info(f"  周期: {stock[2]}")
        else:
            log_error(f"股票 {stock} 数据格式错误，应该包含3个元素")

def test_callback_functions():
    """测试回调函数"""
    log_info("=== 测试回调函数 ===")
    
    # 测试各种回调函数
    def test_wait_func():
        log_info("wait_if_paused_func 被调用")
        time.sleep(0.1)  # 模拟短暂等待
    
    def test_halt_func():
        log_info("should_halt_func 被调用")
        return False  # 不停止
    
    def test_log_func(msg):
        log_info(f"clog_func 被调用: {msg}")
    
    # 测试函数是否可调用
    try:
        test_wait_func()
        log_success("wait_if_paused_func 测试通过")
    except Exception as e:
        log_error(f"wait_if_paused_func 测试失败: {e}")
    
    try:
        result = test_halt_func()
        log_success(f"should_halt_func 测试通过，返回: {result}")
    except Exception as e:
        log_error(f"should_halt_func 测试失败: {e}")
    
    try:
        test_log_func("测试消息")
        log_success("clog_func 测试通过")
    except Exception as e:
        log_error(f"clog_func 测试失败: {e}")

def test_error_handling():
    """测试错误处理"""
    log_info("=== 测试错误处理 ===")
    
    # 测试异常情况
    test_cases = [
        {
            "name": "空股票数据",
            "stock": None,
            "expected_error": True
        },
        {
            "name": "不完整股票数据",
            "stock": ["000001"],
            "expected_error": True
        },
        {
            "name": "正常股票数据但无窗口",
            "stock": ["000001", "平安银行", "60min"],
            "expected_error": True
        }
    ]
    
    for case in test_cases:
        log_info(f"测试用例: {case['name']}")
        
        try:
            result = once_run_body(
                ths_window=None,
                stock=case['stock'],
                wait_if_paused_func=mock_wait_if_paused,
                should_halt_func=mock_should_halt,
                clog_func=mock_clog
            )
            
            if case['expected_error']:
                log_warning(f"预期会出错但成功了: {result}")
            else:
                log_success(f"测试通过: {result}")
                
        except Exception as e:
            if case['expected_error']:
                log_success(f"预期的错误: {e}")
            else:
                log_error(f"意外的错误: {e}")

def main():
    """主测试函数"""
    log_info("开始测试 once_run_body 函数")
    log_info("=" * 60)
    
    test_function_signature()
    test_stock_data_format()
    test_callback_functions()
    test_once_run_body_parameters()
    test_error_handling()
    
    log_success("once_run_body 函数测试完成")
    log_info("=" * 60)
    
    print("\n函数使用说明:")
    print("once_run_body(ths_window, stock, wait_if_paused_func, should_halt_func, clog_func)")
    print("参数说明:")
    print("  ths_window: 同花顺窗口对象")
    print("  stock: 股票信息列表 [代码, 名称, 时间周期]")
    print("  wait_if_paused_func: 暂停等待函数")
    print("  should_halt_func: 停止检查函数")
    print("  clog_func: 日志记录函数")
    print("返回值: [信号值, 状态信息]")

if __name__ == "__main__":
    main()
