import get_stock_pool
import capture_imge
import gen_signal
import open_ths_pyauto
import sql_opeator
from logger_manager import log_info, log_error, log_success, log_warning, log_progress
log_info("running on your personal PC")
import pymysql
import numpy as np
import os
import sub_cycle_body
"""
这个是整个项目的main函数，
主要动作都是由代码完成，
必要步骤用到调用可视版本的子流程来完成，
能用代码完成的尽量用代码完成
"""
import re
import time

def extract_stock_code(code):
    """提取股票代码中的数字部分"""
    match = re.search(r'\d+', code)
    return match.group() if match else code


def once_run_body(ths_window, stock, db_connection_config, wait_if_paused_func, should_halt_func, clog_func):
    """
    执行单次股票处理的主体逻辑

    Args:
        ths_window: 同花顺窗口对象
        stock: 股票信息列表 [代码, 名称, 时间周期]
        wait_if_paused_func: 暂停等待函数
        should_halt_func: 停止检查函数
        clog_func: 日志记录函数

    Returns:
        signal: 信号处理结果
    """
    try:
        # step 4 对单独的这只股票执行可视化操作
        # 传入激活的窗口、股票的名字， 分钟数、和config文件
        log_progress(f"执行股票 {stock[0]}-{stock[1]} 的可视化操作")
        clog_func(f"执行股票 {stock[0]}-{stock[1]} 的可视化操作")
        sub_cycle_body.sub_cycle_body(ths_window, stock)

        wait_if_paused_func()
        if should_halt_func():
            log_warning("检测到停止信号，终止股票处理")
            return [0, "停止信号"]

        # step 5 对窗口进行截图
        log_progress(f"正在截图股票 {stock[0]}")
        clog_func(f"正在截图股票 {stock[0]}")
        image_path = capture_imge.get_img_save_path(stock[0], stock[2])
        screenshot_result = open_ths_pyauto.capture_window_screenshot(ths_window, save_path=image_path[2])

        if screenshot_result is None:
            log_error(f"股票 {stock[0]} 截图失败")
            clog_func(f"股票 {stock[0]} 截图失败")
            return [0, "截图失败"]

        log_success(f"股票 {stock[0]} 截图成功: {image_path[2]}")
        clog_func(f"股票 {stock[0]} 截图成功")

        # Step 6 对截图的信号进行判断，同时存储的sql
        current_file_path = os.path.abspath(__file__)
        current_dir_path = os.path.dirname(current_file_path)
        config_path = os.path.join(current_dir_path, 'resources', 'coor_config.json')

        # 构造股票的字典
        stock_value_dict = {}
        stock_value_dict['code'] = stock[0]
        stock_value_dict['name'] = stock[1]
        stock_value_dict['img_save_path'] = image_path[2]

        # 定义要查询的30个flag字段 (使用数据库中的实际字段名，负数索引)
        flag_fields = [
            'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10', 'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7',
            'LD_flag_-6', 'LD_flag_-5', 'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
            'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10', 'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7',
            'LM_flag_-6', 'LM_flag_-5', 'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1'
        ]
        stock_value_dict['field'] = flag_fields

        # 数据库查询：获取当前股票的flag字段
        try:
            log_progress(f"正在查询股票 {stock[0]} 的历史flag数据")
            clog_func(f"正在查询股票 {stock[0]} 的历史flag数据")

            # 创建数据库操作对象
            sql_manager = sql_opeator.StockDataOperations(table_name='status_signal')

            # 查询当前股票的flag字段
            query_result = sql_manager.query_fields(
                flag_fields,
                where_conditions={'code': stock[0]},
                limit=1
            )

            if query_result and len(query_result) > 0:
                # 如果查询到数据，解析flag值
                result_row = query_result[0]
                ld_flags = []
                lm_flags = []

                # 提取LD标志 (LD_flag_-14 到 LD_flag_-1)
                for i in range(-14, 0):
                    key = f'LD_flag_{i}'
                    if key in result_row and result_row[key] is not None:
                        try:
                            value = float(result_row[key])
                            ld_flags.append(value if not np.isnan(value) else 0.0)
                        except (TypeError, ValueError):
                            ld_flags.append(0.0)
                    else:
                        ld_flags.append(0.0)

                # 提取LM标志 (LM_flag_-14 到 LM_flag_-1)
                for i in range(-14, 0):
                    key = f'LM_flag_{i}'
                    if key in result_row and result_row[key] is not None:
                        try:
                            value = float(result_row[key])
                            lm_flags.append(value if not np.isnan(value) else 0.0)
                        except (TypeError, ValueError):
                            lm_flags.append(0.0)
                    else:
                        lm_flags.append(0.0)

                stock_value_dict['LD_flag_list'] = np.array(ld_flags)
                stock_value_dict['LM_flag_list'] = np.array(lm_flags)

                log_success(f"成功查询到股票 {stock[0]} 的历史flag数据")
                clog_func(f"成功查询到股票 {stock[0]} 的历史flag数据")

            else:
                # 如果没有查询到数据，使用默认零值
                log_info(f"股票 {stock[0]} 在数据库中不存在，使用默认零值初始化")
                clog_func(f"股票 {stock[0]} 在数据库中不存在，使用默认零值初始化")
                stock_value_dict['LD_flag_list'] = np.zeros(15)
                stock_value_dict['LM_flag_list'] = np.zeros(15)

        except Exception as e:
            # 数据库查询异常，使用默认零值
            log_warning(f"查询股票 {stock[0]} 的历史数据时发生异常: {str(e)}，使用默认零值")
            clog_func(f"查询股票 {stock[0]} 的历史数据时发生异常，使用默认零值")
            stock_value_dict['LD_flag_list'] = np.zeros(15)
            stock_value_dict['LM_flag_list'] = np.zeros(15)

        # 处理信号
        log_progress(f"正在分析股票 {stock[0]} 的信号")
        clog_func(f"正在分析股票 {stock[0]} 的信号")

        # stock_value_dict = gen_signal.keep_last_status_sql(stock_value_dict)
        signal = gen_signal.deal_signal_60mins(stock_value_dict, config_path)

        if signal and len(signal) > 0:
            if signal[0] != 0:
                log_success(f"股票 {stock[0]} 检测到信号: {signal}")
                clog_func(f"股票 {stock[0]} 检测到信号: {signal}")
            else:
                log_info(f"股票 {stock[0]} 无信号")
                clog_func(f"股票 {stock[0]} 无信号")

            # 存储signal[1]值到数据库
            try:
                if len(signal) > 1 and signal[1] is not None:
                    log_progress(f"正在存储股票 {stock[0]} 的信号数据到数据库")
                    clog_func(f"正在存储股票 {stock[0]} 的信号数据到数据库")

                    # signal[1] 是 new_data_list，包含 [code, name, LD_flags..., LM_flags...]
                    signal_data = signal[1]

                    # 确保数据格式正确
                    if isinstance(signal_data, (list, tuple, np.ndarray)) and len(signal_data) >= 32:
                        # 转换为列表格式以便数据库操作
                        data_row = [str(item) if not isinstance(item, (int, float)) else item for item in signal_data]

                        # 使用upsert操作存储数据（插入或更新）
                        sql_manager = sql_opeator.StockDataOperations(table_name='status_signal')
                        success = sql_manager.upsert_stock_data([data_row])

                        if success:
                            log_success(f"成功存储股票 {stock[0]} 的信号数据到数据库")
                            clog_func(f"成功存储股票 {stock[0]} 的信号数据到数据库")
                        else:
                            log_warning(f"存储股票 {stock[0]} 的信号数据到数据库失败")
                            clog_func(f"存储股票 {stock[0]} 的信号数据到数据库失败")
                    else:
                        log_warning(f"股票 {stock[0]} 的信号数据格式不正确，跳过数据库存储")
                        clog_func(f"股票 {stock[0]} 的信号数据格式不正确，跳过数据库存储")

            except Exception as e:
                log_error(f"存储股票 {stock[0]} 的信号数据时发生异常: {str(e)}")
                clog_func(f"存储股票 {stock[0]} 的信号数据时发生异常: {str(e)}")

        else:
            log_warning(f"股票 {stock[0]} 信号处理异常")
            clog_func(f"股票 {stock[0]} 信号处理异常")
            signal = [0, "信号处理异常"]

        return signal

    except Exception as e:
        log_error(f"股票 {stock[0] if stock else 'Unknown'} 处理异常: {str(e)}")
        clog_func(f"股票 {stock[0] if stock else 'Unknown'} 处理异常: {str(e)}")
        return [0, f"处理异常: {str(e)}"]

def main(args=None, control=None):
    """主任务入口。
    当提供 control 对象时，支持外部的暂停/停止控制，并回传日志与进度。"""

    def clog(msg: str):
        try:
            if control:
                control.log(msg)
            else:
                log_info(msg)
        except Exception:
            log_error(f"日志记录异常: {msg}")

    def wait_if_paused():
        if not control:
            return
        while control.is_paused and not control.should_stop:
            control.set_status("已暂停")
            time.sleep(0.2)

    def should_halt() -> bool:
        return bool(control and control.should_stop)

    if control:
        control.set_status("初始化...")
        control.set_progress(0)

    log_info("准备启动同花顺进程")
    clog("准备启动同花顺进程")
    if should_halt():
        return

    ############################## step1 prepare ths window   ######################################
    # 启动进程
    log_progress("正在启动同花顺进程...")
    process = open_ths_pyauto.launch_tonghuashun_process()
    if not process:
        log_error("启动同花顺失败")
        clog("启动同花顺失败")
        return

    wait_if_paused()
    if should_halt():
        return

    # 连接窗口
    log_progress("正在连接同花顺窗口...")
    ths_window = open_ths_pyauto.connect_to_window(process)
    if not ths_window:
        log_error("连接窗口失败")
        clog("连接窗口失败")
        return

    # 反复调整窗口到(0,0)和1920x1080，直到成功
    log_progress("正在调整窗口大小和位置...")
    open_ths_pyauto.adjust_window_simple(ths_window, 0, 0, 1920, 1080)


    status = open_ths_pyauto.activate_window_to_front(ths_window, force_activate=True)
    if status is True:
        status = open_ths_pyauto.click_search_box(ths_window, search_text="35", press_enter=True)
        if status is False:
            log_warning("点击搜索框失败")
            clog("点击搜索框失败")
        else:
            log_success("切换到60mins 成功")
            clog("切换到60mins 成功")
    else:
        log_warning("窗口移动到前台失败")
        clog("窗口移动到前台失败")

    if control:
        control.set_status("已连接，准备读取股票列表")

    #################################### step2 prepare favorite stock list #################################
    db_connection_config = {
        'host': '**************',
        'user': 'ken',
        'password': 'li173312',
        'database': 'ths_stock',
        'charset': 'utf8mb4', # 建议总是指定字符集
        'cursorclass': pymysql.cursors.Cursor # 使用标准游标
    }
    stock_list = get_stock_pool.fetch_stock_data(db_connection_config, table_name='favorite_list')

    total_items = len(stock_list) if stock_list else 0
    if control and total_items > 0:
        control.set_status(f"开始处理股票，共{total_items}只")

    # flag = 0
    while True:
    ################################# step3 对股票进行循环    ##################################
        for index, stock_t in enumerate(stock_list):
            clog(f"正在处理股票: {stock_t[0]}-{stock_t[1]}")
            if should_halt():
                clog("检测到停止信号，提前结束外层循环")
                break
            wait_if_paused()

            # 测试
            stock = []
            stock.append(extract_stock_code(stock_t[0]))
            stock.append(stock_t[1])
            stock.append("60min")


            if should_halt():
                clog("检测到停止信号，终止当前股票处理")
                break
            wait_if_paused()

            # 第一次尝试处理股票
            signal = once_run_body(ths_window, stock, db_connection_config, wait_if_paused, should_halt, clog)

            # 如果第一次没有信号，再尝试一次
            if signal[0] == 0:
                log_info(f"股票 {stock[0]} 第一次无信号，重试一次")
                clog(f"股票 {stock[0]} 第一次无信号，重试一次")
                time.sleep(1)  # 短暂等待
                signal = once_run_body(ths_window, stock, db_connection_config, wait_if_paused, should_halt, clog)
                

            # 更新进度
            if control and total_items > 0:
                progress = int((index + 1) / total_items * 100)
                control.set_progress(progress)
                control.set_status(f"处理中... ({index + 1}/{total_items})")

        # 外层循环：如果收到停止信号，则跳出 while True
        if should_halt():
            clog("收到停止信号，退出主循环")
            break

        # 如果需要持续循环，可以在此处短暂休眠，避免空转
        time.sleep(0.1)

    if control:
        control.set_status("任务逻辑结束")

if __name__ == '__main__':
    main([])
