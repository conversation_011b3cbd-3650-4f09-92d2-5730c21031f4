#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式屏幕截图功能演示脚本
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_interactive_screenshot():
    """演示交互式截图功能"""
    print("=" * 60)
    print("交互式屏幕截图功能演示")
    print("=" * 60)
    
    print("本演示将展示如何使用新的交互式屏幕截图功能。")
    print()
    print("功能特点：")
    print("✓ 全局快捷键 Ctrl+Shift+S 触发")
    print("✓ 全屏半透明覆盖层进行区域选择")
    print("✓ 实时显示选择框预览")
    print("✓ 自动截图并保存")
    print("✓ 自动进行K线手动选择")
    print("✓ 更新配置文件")
    print()
    
    print("演示步骤：")
    print("1. 启动交互式截图功能")
    print("2. 在全屏覆盖层上拖拽选择区域")
    print("3. 按回车确认选择")
    print("4. 自动截图并进行K线选择")
    print("5. 查看结果")
    print()
    
    input("按回车键开始演示...")
    
    try:
        # 导入功能模块
        from windows_precessing import interactive_screenshot_capture
        
        print("正在启动交互式截图功能...")
        print("请在屏幕上选择一个区域进行截图")
        print("(按ESC可以取消选择)")
        
        # 执行交互式截图
        result = interactive_screenshot_capture()
        
        if result:
            print(f"\n✓ 演示成功完成！")
            print(f"✓ 截图已保存到: {result}")
            print(f"✓ 配置文件已更新")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✓ 文件大小: {file_size} 字节")
            
            # 显示配置文件更新信息
            show_config_update()
            
        else:
            print("\n演示被取消或失败")
            
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保 windows_precessing.py 文件存在且正确")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")

def show_config_update():
    """显示配置文件更新信息"""
    try:
        import json
        config_path = "resources/coor_config.json"
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if "interactive_capture" in config:
                print("\n配置文件更新信息：")
                interactive_config = config["interactive_capture"]
                
                if "timestamp" in interactive_config:
                    print(f"✓ 更新时间: {interactive_config['timestamp']}")
                
                if "last_screenshot_path" in interactive_config:
                    print(f"✓ 截图路径: {interactive_config['last_screenshot_path']}")
                
                if "original_selection" in interactive_config:
                    orig = interactive_config["original_selection"]
                    print(f"✓ 原始选择区域: {orig['p1']} 到 {orig['p2']}")
                
                if "kline_selection" in interactive_config:
                    kline = interactive_config["kline_selection"]
                    print(f"✓ K线选择区域: {kline['p1']} 到 {kline['p2']}")
            else:
                print("配置文件中未找到交互式截图信息")
        else:
            print("配置文件不存在")
            
    except Exception as e:
        print(f"读取配置文件时发生错误: {e}")

def demo_hotkey_mode():
    """演示快捷键模式"""
    print("=" * 60)
    print("全局快捷键模式演示")
    print("=" * 60)
    
    print("本演示将启动全局快捷键监听模式。")
    print()
    print("使用方法：")
    print("1. 程序将在后台运行")
    print("2. 在任何时候按 Ctrl+Shift+S 触发截图")
    print("3. 按 Ctrl+C 退出程序")
    print()
    
    confirm = input("是否启动快捷键监听模式？(y/N): ").strip().lower()
    
    if confirm == 'y':
        try:
            from windows_precessing import setup_global_hotkey
            
            print("正在启动全局快捷键监听...")
            print("按 Ctrl+Shift+S 进行截图，按 Ctrl+C 退出")
            
            setup_global_hotkey()
            
        except ImportError as e:
            print(f"导入模块失败: {e}")
        except Exception as e:
            print(f"启动快捷键监听时发生错误: {e}")
    else:
        print("已取消快捷键模式演示")

def main():
    """主函数"""
    print("交互式屏幕截图功能演示程序")
    print("=" * 40)
    
    while True:
        print("\n选择演示模式：")
        print("1. 演示交互式截图功能")
        print("2. 演示全局快捷键模式")
        print("3. 查看功能说明")
        print("4. 退出")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                demo_interactive_screenshot()
            elif choice == '2':
                demo_hotkey_mode()
            elif choice == '3':
                show_feature_description()
            elif choice == '4':
                print("退出演示程序")
                break
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n演示被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

def show_feature_description():
    """显示功能说明"""
    print("=" * 60)
    print("交互式屏幕截图功能说明")
    print("=" * 60)
    
    features = [
        "🎯 全局快捷键触发 (Ctrl+Shift+S)",
        "🖥️ 全屏半透明覆盖层",
        "🖱️ 鼠标拖拽选择区域",
        "📸 自动截图保存",
        "📊 自动K线区域选择",
        "⚙️ 自动更新配置文件",
        "🔄 与现有功能完全兼容"
    ]
    
    print("主要功能特点：")
    for feature in features:
        print(f"  {feature}")
    
    print("\n操作流程：")
    steps = [
        "启动程序或按快捷键",
        "在全屏覆盖层上拖拽选择区域",
        "按回车确认选择",
        "程序自动截图",
        "自动打开截图进行K线选择",
        "程序自动处理并更新配置"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"  {i}. {step}")
    
    print("\n文件保存位置：")
    print("  📁 截图文件: resources/images/")
    print("  📄 配置文件: resources/coor_config.json")
    
    input("\n按回车键返回主菜单...")

if __name__ == "__main__":
    main()
