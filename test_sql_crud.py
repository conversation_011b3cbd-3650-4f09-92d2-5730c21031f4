#!/usr/bin/env python3
"""
测试SQL CRUD操作的简单脚本
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sql_opeator
from logger_manager import log_info, log_error, log_success, log_warning

def test_sql_crud_operations():
    """测试SQL CRUD操作"""
    
    # 数据库连接配置
    db_connection_config = {
        'host': '**************',
        'user': 'ken',
        'password': 'li173312',
        'database': 'ths_stock',
        'charset': 'utf8mb4',
        'cursorclass': 'pymysql.cursors.Cursor'
    }
    
    # 测试股票代码
    test_stock_code = "000001"
    
    # 定义要查询的30个flag字段 (使用数据库中的实际字段名，负数索引)
    flag_fields = [
        'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10', 'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7',
        'LD_flag_-6', 'LD_flag_-5', 'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
        'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10', 'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7',
        'LM_flag_-6', 'LM_flag_-5', 'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1'
    ]
    
    try:
        log_info("开始测试SQL CRUD操作...")
        
        # 创建数据库操作对象
        sql_manager = sql_opeator.StockDataOperations(table_name='status_signal')
        
        # 1. 测试查询操作
        log_info(f"正在查询股票 {test_stock_code} 的历史flag数据")
        
        query_result = sql_manager.query_fields(
            flag_fields,
            where_conditions={'code': test_stock_code},
            limit=1
        )
        
        if query_result and len(query_result) > 0:
            log_success(f"成功查询到股票 {test_stock_code} 的数据")
            result_row = query_result[0]
            
            # 解析flag值
            ld_flags = []
            lm_flags = []
            
            # 提取LD标志 (LD_flag_-14 到 LD_flag_-1)
            for i in range(-14, 0):
                key = f'LD_flag_{i}'
                if key in result_row and result_row[key] is not None:
                    try:
                        value = float(result_row[key])
                        ld_flags.append(value if not np.isnan(value) else 0.0)
                    except (TypeError, ValueError):
                        ld_flags.append(0.0)
                else:
                    ld_flags.append(0.0)
            
            # 提取LM标志 (LM_flag_-14 到 LM_flag_-1)
            for i in range(-14, 0):
                key = f'LM_flag_{i}'
                if key in result_row and result_row[key] is not None:
                    try:
                        value = float(result_row[key])
                        lm_flags.append(value if not np.isnan(value) else 0.0)
                    except (TypeError, ValueError):
                        lm_flags.append(0.0)
                else:
                    lm_flags.append(0.0)
            
            log_info(f"LD_flags: {ld_flags}")
            log_info(f"LM_flags: {lm_flags}")
            
        else:
            log_warning(f"股票 {test_stock_code} 在数据库中不存在，使用默认零值初始化")
            ld_flags = np.zeros(14).tolist()
            lm_flags = np.zeros(14).tolist()
        
        # 2. 测试存储操作
        log_info("测试存储操作...")
        
        # 模拟signal[1]数据 (包含 [code, name, LD_flags..., LM_flags...])
        test_signal_data = [test_stock_code, "测试股票"] + ld_flags + lm_flags
        
        # 确保数据长度正确 (2 + 14 + 14 = 30)
        if len(test_signal_data) >= 30:
            success = sql_manager.upsert_stock_data([test_signal_data])
            
            if success:
                log_success(f"成功存储股票 {test_stock_code} 的测试数据到数据库")
            else:
                log_error(f"存储股票 {test_stock_code} 的测试数据到数据库失败")
        else:
            log_error(f"测试数据格式不正确，长度: {len(test_signal_data)}")
        
        log_success("SQL CRUD操作测试完成")
        
    except Exception as e:
        log_error(f"测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sql_crud_operations()
