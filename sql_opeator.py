# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

try:
    import xbot
    from xbot import print, sleep
    from .import package
    from .package import variables as glv
except:
    print("runing on your personal PC")
import pymysql
from pymysql.cursors import DictCursor
from typing import List, Dict, Any, Optional, Union

# def main(args):
#     """主函数示例"""
#     # 初始化数据库操作类
#     db_ops = StockDataOperations()
    
#     # 示例数据
#     test_data = [
#         ["000001", "平安银行", 1, 0, None, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
#     ]
    
#     # 测试各种操作
#     print("=== 测试数据库操作 ===")
    
#     # 1. 插入数据
#     print("\n1. 插入测试数据...")
#     db_ops.upsert_stock_data(test_data)
    
#     # 2. 查询单个字段
#     print("\n2. 查询单个字段...")
#     result = db_ops.query_fields(['name'], where_conditions={'code': '000001'})
#     print(f"查询结果: {result}")
    
#     # 3. 查询多个字段
#     print("\n3. 查询多个字段...")
#     result = db_ops.query_fields(['code', 'name', 'LD_flag_-1'], limit=5)
#     print(f"查询结果: {result}")
    
#     # 4. 更新数据
#     print("\n4. 更新数据...")
#     db_ops.update_stock_data({'name': '平安银行(更新)'}, {'code': '000001'})
    
#     # 5. 验证更新
#     print("\n5. 验证更新结果...")
#     result = db_ops.query_fields(['code', 'name'], where_conditions={'code': '000001'})
#     print(f"更新后结果: {result}")


class DatabaseManager:
    """
    一个简单的数据库连接上下文管理器。
    - 初始化时接收数据库配置。
    - 使用 'with' 语句可以自动连接、提交/回滚和关闭连接。
    """
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None
        self.cursor = None

    def __enter__(self):
        """当进入 'with' 语句块时调用，建立连接并返回游标。"""
        try:
            self.conn = pymysql.connect(**self.db_config)
            self.cursor = self.conn.cursor(DictCursor)  # 使用字典游标便于处理结果
            return self.cursor
        except pymysql.MySQLError as e:
            print(f"数据库连接失败: {e}")
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """当退出 'with' 语句块时调用，处理事务和关闭连接。"""
        if exc_type:
            print(f"发生异常，事务将回滚: {exc_val}")
            if self.conn:
                self.conn.rollback()
        else:
            if self.conn:
                self.conn.commit()
                print("事务已成功提交。")
        
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("数据库连接已关闭。")
        return False


class StockDataOperations:
    """股票数据库操作类 - 提供完整的CRUD功能"""
    
    def __init__(self, table_name, custom_config: Optional[Dict] = None):
        """
        初始化数据库操作类
        
        Args:
            custom_config: 自定义数据库配置，如果不提供则使用默认配置
        """
        self.db_config = custom_config or {
            'host': '**************',
            'user': 'ken',
            'password': 'li173312',
            'database': 'ths_stock',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        self.table_name = table_name
        self.all_fields = [
            'code', 'name', 
            'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10',
            'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7', 'LD_flag_-6', 'LD_flag_-5',
            'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
            'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10',
            'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7', 'LM_flag_-6', 'LM_flag_-5',
            'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1'
        ]

    def _build_where_clause(self, conditions: Dict[str, Any]) -> tuple:
        """
        构建WHERE子句
        
        Args:
            conditions: 查询条件字典 {'field': 'value'}
            
        Returns:
            (where_clause_str, values_tuple)
        """
        if not conditions:
            return "", ()
        
        where_parts = []
        values = []
        
        for field, value in conditions.items():
            if value is None:
                where_parts.append(f"`{field}` IS NULL")
            else:
                where_parts.append(f"`{field}` = %s")
                values.append(value)
        
        where_clause = " WHERE " + " AND ".join(where_parts)
        return where_clause, tuple(values)

    def query_fields(self, 
                    fields: List[str], 
                    where_conditions: Optional[Dict[str, Any]] = None,
                    order_by: Optional[str] = None,
                    limit: Optional[int] = None) -> List[Dict]:
        """
        查询指定字段
        
        Args:
            fields: 要查询的字段列表 ['code', 'name']
            where_conditions: 查询条件 {'code': '000001', 'name': '平安'}
            order_by: 排序字段，如 'code ASC' 或 'name DESC'
            limit: 限制返回行数
            
        Returns:
            查询结果列表，每个元素是一个字典
        """
        try:
            # 验证字段名
            invalid_fields = [f for f in fields if f not in self.all_fields]
            if invalid_fields:
                print(f"警告：无效的字段名: {invalid_fields}")
                fields = [f for f in fields if f in self.all_fields]
            
            if not fields:
                print("错误：没有有效的查询字段")
                return []
            
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                # 构建查询语句
                fields_str = ', '.join([f"`{f}`" for f in fields])
                query = f"SELECT {fields_str} FROM {self.table_name}"
                
                # 添加WHERE条件
                where_clause, where_values = self._build_where_clause(where_conditions or {})
                query += where_clause
                
                # 添加ORDER BY
                if order_by:
                    query += f" ORDER BY {order_by}"
                
                # 添加LIMIT
                if limit:
                    query += f" LIMIT {limit}"
                
                print(f"执行查询: {query}")
                if where_values:
                    print(f"参数值: {where_values}")
                
                cursor.execute(query, where_values)
                results = cursor.fetchall()
                
                print(f"查询完成，共返回 {len(results)} 条记录")
                return results
                
        except pymysql.MySQLError as e:
            print(f"查询失败: {e}")
            return []
        except Exception as e:
            print(f"查询发生未知错误: {e}")
            return []

    def query_all(self, 
                  where_conditions: Optional[Dict[str, Any]] = None,
                  order_by: Optional[str] = None,
                  limit: Optional[int] = None) -> List[Dict]:
        """
        查询所有字段
        
        Args:
            where_conditions: 查询条件
            order_by: 排序字段
            limit: 限制返回行数
            
        Returns:
            查询结果列表
        """
        return self.query_fields(self.all_fields, where_conditions, order_by, limit)

    def insert_stock_data(self, data_rows: List[List]) -> bool:
        """
        插入新的股票数据（如果已存在则忽略）- 完整行插入
        
        Args:
            data_rows: 数据行列表，每行数据对应all_fields的顺序
            
        Returns:
            是否成功
        """
        if not self._validate_data_format(data_rows):
            return False
        
        try:
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                self._ensure_table_structure(cursor)
                
                fields_str = ', '.join([f"`{f}`" for f in self.all_fields])
                placeholders = ', '.join(['%s'] * len(self.all_fields))
                
                # 使用INSERT IGNORE避免重复插入
                query = f"INSERT IGNORE INTO {self.table_name} ({fields_str}) VALUES ({placeholders})"
                
                data_tuples = [tuple(row) for row in data_rows]
                rows_affected = cursor.executemany(query, data_tuples)
                
                print(f"插入操作完成，{rows_affected} 条新记录被插入")
                return True
                
        except pymysql.MySQLError as e:
            print(f"插入数据失败: {e}")
            return False
        except Exception as e:
            print(f"插入数据发生未知错误: {e}")
            return False

    def insert_partial_data(self, data_records: List[Dict[str, Any]], ignore_duplicates: bool = True) -> bool:
        """
        插入部分字段的数据（支持单字段或多字段）
        
        Args:
            data_records: 数据记录列表，每个记录是字典格式 [{'code': '000001', 'name': '平安'}, ...]
            ignore_duplicates: 是否忽略重复数据（使用INSERT IGNORE）
            
        Returns:
            是否成功
        """
        if not data_records:
            print("错误：数据记录不能为空")
            return False
        
        # 验证所有记录的字段是否一致
        first_record_fields = set(data_records[0].keys())
        for i, record in enumerate(data_records):
            if set(record.keys()) != first_record_fields:
                print(f"错误：第 {i+1} 条记录的字段与第1条不一致")
                return False
        
        # 验证字段名有效性
        invalid_fields = [f for f in first_record_fields if f not in self.all_fields]
        if invalid_fields:
            print(f"错误：无效的字段名: {invalid_fields}")
            return False
        
        # code字段是必需的（作为主键）
        if 'code' not in first_record_fields:
            print("错误：必须包含code字段（主键）")
            return False
        
        try:
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                self._ensure_table_structure(cursor)
                
                # 构建插入语句
                fields_list = list(first_record_fields)
                fields_str = ', '.join([f"`{f}`" for f in fields_list])
                placeholders = ', '.join(['%s'] * len(fields_list))
                
                insert_type = "INSERT IGNORE" if ignore_duplicates else "INSERT"
                query = f"{insert_type} INTO {self.table_name} ({fields_str}) VALUES ({placeholders})"
                
                # 准备数据
                data_tuples = []
                for record in data_records:
                    data_tuple = tuple(record[field] for field in fields_list)
                    data_tuples.append(data_tuple)
                
                print(f"执行插入: {query}")
                print(f"插入字段: {fields_list}")
                print(f"数据条数: {len(data_tuples)}")
                
                rows_affected = cursor.executemany(query, data_tuples)
                print(f"部分字段插入完成，{rows_affected} 条记录被处理")
                
                return True
                
        except pymysql.MySQLError as e:
            print(f"插入部分数据失败: {e}")
            return False
        except Exception as e:
            print(f"插入部分数据发生未知错误: {e}")
            return False

    def upsert_stock_data(self, data_rows: List[List]) -> bool:
        """
        插入或更新股票数据（原有功能保持不变）
        
        Args:
            data_rows: 数据行列表
            
        Returns:
            是否成功
        """
        if not self._validate_data_format(data_rows):
            return False
        
        try:
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                # 确保表结构正确
                self._ensure_table_structure(cursor)
                
                # 构建UPSERT语句
                fields_str = ', '.join([f"`{f}`" for f in self.all_fields])
                placeholders = ', '.join(['%s'] * len(self.all_fields))
                
                update_parts = []
                for field in self.all_fields:
                    if field != 'code':  # code字段作为主键不更新
                        update_parts.append(f"`{field}` = VALUES(`{field}`)")
                update_clause = ', '.join(update_parts)
                
                query = (
                    f"INSERT INTO {self.table_name} ({fields_str}) "
                    f"VALUES ({placeholders}) "
                    f"ON DUPLICATE KEY UPDATE {update_clause}"
                )
                
                data_tuples = [tuple(row) for row in data_rows]
                rows_affected = cursor.executemany(query, data_tuples)
                
                print(f"UPSERT操作完成，{rows_affected} 条记录被处理")
                return True
                
        except pymysql.MySQLError as e:
            print(f"UPSERT操作失败: {e}")
            return False
        except Exception as e:
            print(f"UPSERT操作发生未知错误: {e}")
            return False

    def update_stock_data(self, 
                         update_data: Dict[str, Any], 
                         where_conditions: Dict[str, Any]) -> bool:
        """
        更新股票数据
        
        Args:
            update_data: 要更新的数据 {'name': '新名称', 'LD_flag_-1': 1}
            where_conditions: 更新条件 {'code': '000001'}
            
        Returns:
            是否成功
        """
        if not update_data or not where_conditions:
            print("错误：更新数据和查询条件不能为空")
            return False
        
        # 验证字段名
        invalid_fields = [f for f in update_data.keys() if f not in self.all_fields]
        if invalid_fields:
            print(f"错误：无效的字段名: {invalid_fields}")
            return False
        
        try:
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                # 构建UPDATE语句
                set_parts = []
                set_values = []
                for field, value in update_data.items():
                    if value is None:
                        set_parts.append(f"`{field}` = NULL")
                    else:
                        set_parts.append(f"`{field}` = %s")
                        set_values.append(value)
                
                set_clause = ', '.join(set_parts)
                where_clause, where_values = self._build_where_clause(where_conditions)
                
                query = f"UPDATE {self.table_name} SET {set_clause}{where_clause}"
                all_values = set_values + list(where_values)
                
                print(f"执行更新: {query}")
                print(f"参数值: {all_values}")
                
                rows_affected = cursor.execute(query, all_values)
                print(f"更新完成，{rows_affected} 条记录被更新")
                
                return True
                
        except pymysql.MySQLError as e:
            print(f"更新数据失败: {e}")
            return False
        except Exception as e:
            print(f"更新数据发生未知错误: {e}")
            return False

    def delete_stock_data(self, where_conditions: Dict[str, Any]) -> bool:
        """
        删除股票数据
        
        Args:
            where_conditions: 删除条件 {'code': '000001'} 或 {'name': '平安银行'}
            
        Returns:
            是否成功
        """
        if not where_conditions:
            print("错误：删除条件不能为空，这是安全限制")
            return False
        
        try:
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                where_clause, where_values = self._build_where_clause(where_conditions)
                query = f"DELETE FROM {self.table_name}{where_clause}"
                
                print(f"执行删除: {query}")
                print(f"参数值: {where_values}")
                
                # 先查询将要删除的记录数
                count_query = f"SELECT COUNT(*) as count FROM {self.table_name}{where_clause}"
                cursor.execute(count_query, where_values)
                count_result = cursor.fetchone()
                will_delete = count_result['count'] if count_result else 0
                
                if will_delete == 0:
                    print("没有找到符合条件的记录")
                    return True
                
                # 确认删除（在实际应用中可能需要更复杂的确认机制）
                print(f"警告：将要删除 {will_delete} 条记录")
                
                rows_affected = cursor.execute(query, where_values)
                print(f"删除完成，{rows_affected} 条记录被删除")
                
                return True
                
        except pymysql.MySQLError as e:
            print(f"删除数据失败: {e}")
            return False
        except Exception as e:
            print(f"删除数据发生未知错误: {e}")
            return False

    def get_table_info(self) -> Dict[str, Any]:
        """
        获取表信息
        
        Returns:
            表信息字典，包含字段信息、记录数等
        """
        try:
            db_manager = DatabaseManager(self.db_config)
            with db_manager as cursor:
                info = {
                    'table_name': self.table_name,
                    'fields': [],
                    'total_records': 0,
                    'field_count': 0
                }
                
                # 获取字段信息
                cursor.execute(f"SHOW COLUMNS FROM {self.table_name}")
                columns = cursor.fetchall()
                info['fields'] = columns
                info['field_count'] = len(columns)
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) as count FROM {self.table_name}")
                count_result = cursor.fetchone()
                info['total_records'] = count_result['count'] if count_result else 0
                
                return info
                
        except pymysql.MySQLError as e:
            print(f"获取表信息失败: {e}")
            return {}
        except Exception as e:
            print(f"获取表信息发生未知错误: {e}")
            return {}

    def _validate_data_format(self, data_rows: List[List]) -> bool:
        """验证数据格式"""
        expected_length = len(self.all_fields)
        for i, row in enumerate(data_rows):
            if len(row) != expected_length:
                print(f"错误: 第 {i+1} 行数据长度为 {len(row)}，期望长度为 {expected_length}")
                return False
        return True

    def _ensure_table_structure(self, cursor):
        """确保表结构正确"""
        cursor.execute(f"SHOW COLUMNS FROM {self.table_name}")
        existing_fields = {row['Field'] for row in cursor.fetchall()}
        
        missing_fields = set(self.all_fields) - existing_fields
        if missing_fields:
            print(f"发现缺失字段: {', '.join(missing_fields)}")
            for field in missing_fields:
                if 'flag' in field:
                    query = f"ALTER TABLE {self.table_name} ADD COLUMN `{field}` INT NULL DEFAULT NULL"
                    cursor.execute(query)
            print("表结构同步完成")


# === 便捷函数接口（保持向后兼容） ===

def upsert_stock_data(table_name: str, data_to_write: List[List]) -> bool:
    """
    便捷函数：插入或更新股票数据（保持原有接口不变）
    """
    db_ops = StockDataOperations(table_name)
    return db_ops.upsert_stock_data(data_to_write)

def query_stock_fields(table_name:str, fields: List[str], 
                      where_conditions: Optional[Dict[str, Any]] = None,
                      limit: Optional[int] = None) -> List[Dict]:
    """
    便捷函数：查询指定字段
    """
    db_ops = StockDataOperations(table_name)
    return db_ops.query_fields(fields, where_conditions, limit=limit)

def update_stock_record(table_name: str, update_data: Dict[str, Any], 
                       where_conditions: Dict[str, Any]) -> bool:
    """
    便捷函数：更新股票记录
    """
    db_ops = StockDataOperations(table_name)
    return db_ops.update_stock_data(update_data, where_conditions)

def insert_partial_stock_data(table_name: str, data_records: List[Dict[str, Any]], 
                             ignore_duplicates: bool = True) -> bool:
    """
    便捷函数：插入部分字段的股票数据
    """
    db_ops = StockDataOperations(table_name)
    return db_ops.insert_partial_data(data_records, ignore_duplicates)


def delete_stock_record(table_name:str, where_conditions: Dict[str, Any]) -> bool:
    """
    便捷函数：删除股票记录
    """
    db_ops = StockDataOperations(table_name)
    return db_ops.delete_stock_data(where_conditions)


# === 使用示例 ===
if __name__ == "__main__":
    # 创建数据库操作对象
    table_name='status_signal'
    db_ops = StockDataOperations(table_name)
    
    # 完整行数据示例
    full_data = [
        ["000001", "平安银行", 1, 0, None, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
        ["000002", "万科A", 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]
    ]
    
    # 部分字段数据示例
    partial_data = [
        {'code': '000003', 'name': '万科B', 'LD_flag_-1': 1, 'LM_flag_-1': 0},
        {'code': '000004', 'name': '国农科技', 'LD_flag_-1': 0, 'LM_flag_-1': 1}
    ]
    
    print("=== 完整功能测试（包含多字段支持） ===")
    
    # 1. 完整行插入
    print("1. 完整行插入...")
    db_ops.upsert_stock_data(table_name, full_data)
    
    # 2. 部分字段插入（新功能）
    print("\n2. 部分字段插入...")
    db_ops.insert_partial_data(table_name, partial_data)
    
    # 3. 查询单个字段
    print("\n3. 查询单个字段(name)...")
    results = db_ops.query_fields(['name'], limit=3)
    for result in results:
        print(f"  {result}")
    
    # 4. 查询多个字段带条件
    print("\n4. 查询多个字段带条件...")
    results = db_ops.query_fields(table_name,
        ['code', 'name', 'LD_flag_-1'], 
        where_conditions={'code': '000003'}
    )
    for result in results:
        print(f"  {result}")
    
    # 5. 更新单个字段
    print("\n5. 更新单个字段...")
    db_ops.update_stock_data(table_name, {'name': '万科B(更新)'}, {'code': '000003'})
    
    # 6. 更新多个字段
    print("\n6. 更新多个字段...")
    db_ops.update_stock_data(table_name,
        {'name': '国农科技(更新)', 'LD_flag_-1': 1, 'LM_flag_-1': 0}, 
        {'code': '000004'}
    )
    
    # 7. 验证更新（查询多个字段）
    print("\n7. 验证更新...")
    results = db_ops.query_fields(table_name,['code', 'name', 'LD_flag_-1', 'LM_flag_-1'])
    for result in results:
        print(f"  {result}")
    
    # 8. 条件删除（支持多字段条件）
    print("\n8. 条件删除示例（这里只演示，不实际执行）...")
    print("  单字段条件删除: db_ops.delete_stock_data({'code': '000003'})")
    print("  多字段条件删除: db_ops.delete_stock_data({'code': '000004', 'name': '国农科技(更新)'})")
    
    # 9. 使用便捷函数
    print("\n9. 使用便捷函数...")
    
    # 便捷查询
    results = query_stock_fields(['code', 'name'], limit=2)
    print("便捷查询结果:")
    for result in results:
        print(f"  {result}")
    
    # 便捷部分插入
    new_partial_data = [table_name, {'code': '000005', 'name': '世纪星源'}]
    print("便捷部分字段插入...")
    insert_partial_stock_data(new_partial_data)
    
    print("\n=== 多字段支持总结 ===")
    print("✅ 查询(Read): 支持查询任意单/多字段组合")
    print("✅ 插入(Create): 支持完整行插入 + 部分字段插入")
    print("✅ 更新(Update): 支持更新任意单/多字段组合")
    print("✅ 删除(Delete): 支持单/多字段条件删除")

def main(args):
    # 创建数据库操作对象
    table_name='status_signal'
    db_ops = StockDataOperations(table_name='status_signal')
    
    # 完整行数据示例
    full_data = [
        ["000001", "平安银行", 1, 0, None, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
        ["000002", "万科A", 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]
    ]
    
    # 部分字段数据示例
    partial_data = [
        {'code': '000003', 'name': '万科B', 'LD_flag_-1': 1, 'LM_flag_-1': 0},
        {'code': '000004', 'name': '国农科技', 'LD_flag_-1': 0, 'LM_flag_-1': 1}
    ]
    
    print("=== 完整功能测试（包含多字段支持） ===")
    
    # 1. 完整行插入
    print("1. 完整行插入...")
    db_ops.upsert_stock_data(full_data)
    
    # 2. 部分字段插入（新功能）
    print("\n2. 部分字段插入...")
    db_ops.insert_partial_data(partial_data)
    
    # 3. 查询单个字段
    print("\n3. 查询单个字段(name)...")
    results = db_ops.query_fields(['name'], limit=3)
    for result in results:
        print(f"  {result}")
    
    # 4. 查询多个字段带条件
    print("\n4. 查询多个字段带条件...")
    results = db_ops.query_fields(
        ['code', 'name', 'LD_flag_-1'], 
        where_conditions={'code': '000003'}
    )
    for result in results:
        print(f"  {result}")
    
    # 5. 更新单个字段
    print("\n5. 更新单个字段...")
    db_ops.update_stock_data({'name': '万科B(更新)'}, {'code': '000003'})
    
    # 6. 更新多个字段
    print("\n6. 更新多个字段...")
    db_ops.update_stock_data(
        {'name': '国农科技(更新)', 'LD_flag_-1': 1, 'LM_flag_-1': 0}, 
        {'code': '000004'}
    )
    
    # 7. 验证更新（查询多个字段）
    print("\n7. 验证更新...")
    results = db_ops.query_fields(['code', 'name', 'LD_flag_-1', 'LM_flag_-1'])
    for result in results:
        print(f"  {result}")
    
    # 8. 条件删除（支持多字段条件）
    print("\n8. 条件删除示例（这里只演示，不实际执行）...")
    print("  单字段条件删除: db_ops.delete_stock_data({'code': '000003'})")
    print("  多字段条件删除: db_ops.delete_stock_data({'code': '000004', 'name': '国农科技(更新)'})")
    
    # 9. 使用便捷函数
    print("\n9. 使用便捷函数...")
    
    # 便捷查询
    results = query_stock_fields(table_name, ['code', 'name'], limit=2)
    print("便捷查询结果:")
    for result in results:
        print(f"  {result}")
    
    # 便捷部分插入
    new_partial_data = [{'code': '000005', 'name': '世纪星源'}]
    print("便捷部分字段插入...")
    insert_partial_stock_data(table_name,new_partial_data)
    
    print("\n=== 多字段支持总结 ===")
    print("✅ 查询(Read): 支持查询任意单/多字段组合")
    print("✅ 插入(Create): 支持完整行插入 + 部分字段插入")
    print("✅ 更新(Update): 支持更新任意单/多字段组合")
    print("✅ 删除(Delete): 支持单/多字段条件删除")
