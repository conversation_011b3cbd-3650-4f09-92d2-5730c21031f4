# 手动线条检测功能说明

## 功能概述

`detect_and_predict_vertical_lines` 函数已经升级，新增了手动选择垂直线位置的功能。用户现在可以在自动检测精度不够时，切换到手动模式通过鼠标点击来精确选择垂直线位置。

## 函数签名

```python
def detect_and_predict_vertical_lines(image_path, mode='auto', vertical_angle_tolerance=10, spacing_cluster_tolerance=10):
    """
    检测图片中的所有竖线，将检测到的线作为基准，智能地填充和延伸缺失的线条。

    参数:
    image_path (str): 输入图片的路径。
    mode (str): 检测模式，'auto' 为自动检测，'manual' 为手动选择。
    vertical_angle_tolerance (int): 竖线角度的容忍度（单位：度）。
    spacing_cluster_tolerance (int): 间距聚类时的容差（像素）。
    
    返回:
    list: 垂直线的x坐标列表
    """
```

## 使用方法

### 1. 自动检测模式（默认）

```python
import lines_detetor

# 方法1：使用默认参数（向后兼容）
result = lines_detetor.detect_and_predict_vertical_lines("image.jpg")

# 方法2：显式指定自动模式
result = lines_detetor.detect_and_predict_vertical_lines("image.jpg", mode='auto')

# 方法3：自定义参数
result = lines_detetor.detect_and_predict_vertical_lines(
    "image.jpg", 
    mode='auto',
    vertical_angle_tolerance=5,
    spacing_cluster_tolerance=3
)
```

### 2. 手动选择模式

```python
import lines_detetor

# 启用手动选择模式
result = lines_detetor.detect_and_predict_vertical_lines("image.jpg", mode='manual')
```

## 手动选择模式操作指南

### 启动手动模式
1. 调用函数时设置 `mode='manual'`
2. 程序会弹出图像显示窗口
3. 窗口标题显示为 "Manual Line Selection"

### 选择垂直线位置
1. **鼠标点击**：在图像上点击鼠标左键选择垂直线位置
2. **视觉反馈**：每次点击会在图像上绘制绿色垂直线
3. **序号标记**：每条线的顶部会显示选择序号（1, 2, 3...）
4. **进度提示**：控制台会显示当前已选择的点数

### 完成选择
- **推荐方式**：选择15个点后，程序会自动提示完成
- **手动完成**：选择任意数量的点后，按任意键（除ESC外）完成
- **取消选择**：按ESC键取消选择并返回空列表

### 选择建议
- **数量**：建议选择15个垂直线位置以获得最佳效果
- **分布**：尽量在图像宽度范围内均匀分布选择点
- **精度**：点击时尽量准确，选择明显的垂直线特征位置

## 错误处理

### 选择点数不足
如果选择的点数少于15个，程序会：
1. 显示警告信息
2. 询问用户是否继续使用这些点
3. 用户可以选择继续或重新开始

### 取消选择
- 按ESC键可以取消选择
- 函数会返回空列表 `[]`

### 无效模式
如果指定了不支持的模式，函数会：
1. 显示错误信息
2. 返回空列表 `[]`

## 输出格式

两种模式的输出格式完全一致：

```python
# 返回值示例
[100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800]
```

- 返回类型：`list`
- 元素类型：`int`（x坐标像素值）
- 排序：按x坐标从小到大排序

## 向后兼容性

✅ **完全向后兼容**：现有代码无需修改即可正常工作

```python
# 现有代码继续正常工作
result = lines_detetor.detect_and_predict_vertical_lines("image.jpg")
# 等同于
result = lines_detetor.detect_and_predict_vertical_lines("image.jpg", mode='auto')
```

## 使用场景

### 自动模式适用场景
- 图像质量良好，垂直线清晰
- 垂直线间距相对规律
- 批量处理大量图像
- 对精度要求不是特别高

### 手动模式适用场景
- 自动检测结果不准确
- 图像质量较差或有干扰
- 垂直线不规律或部分缺失
- 需要高精度的垂直线位置
- 调试和验证自动检测结果

## 性能对比

| 模式 | 速度 | 精度 | 人工干预 | 适用场景 |
|------|------|------|----------|----------|
| 自动模式 | 快 | 中等 | 无 | 批量处理 |
| 手动模式 | 慢 | 高 | 需要 | 精确定位 |

## 代码示例

### 完整使用示例

```python
import lines_detetor

def process_image_with_fallback(image_path):
    """
    处理图像，自动模式失败时回退到手动模式
    """
    print(f"处理图像: {image_path}")
    
    # 首先尝试自动模式
    print("尝试自动检测...")
    auto_result = lines_detetor.detect_and_predict_vertical_lines(
        image_path, mode='auto'
    )
    
    if auto_result and len(auto_result) >= 10:
        print(f"自动检测成功，检测到 {len(auto_result)} 条线")
        return auto_result
    else:
        print("自动检测结果不理想，切换到手动模式...")
        manual_result = lines_detetor.detect_and_predict_vertical_lines(
            image_path, mode='manual'
        )
        
        if manual_result:
            print(f"手动选择完成，选择了 {len(manual_result)} 条线")
            return manual_result
        else:
            print("手动选择被取消")
            return []

# 使用示例
image_path = "your_image.jpg"
result = process_image_with_fallback(image_path)
print(f"最终结果: {result}")
```

### 批量处理示例

```python
import os
import lines_detetor

def batch_process_images(image_dir, mode='auto'):
    """
    批量处理图像目录中的所有图像
    """
    results = {}
    
    for filename in os.listdir(image_dir):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_path = os.path.join(image_dir, filename)
            print(f"处理: {filename}")
            
            result = lines_detetor.detect_and_predict_vertical_lines(
                image_path, mode=mode
            )
            
            results[filename] = result
            print(f"  检测到 {len(result)} 条线")
    
    return results

# 批量自动处理
auto_results = batch_process_images("images/", mode='auto')

# 对失败的图像进行手动处理
for filename, result in auto_results.items():
    if len(result) < 10:  # 如果检测结果不理想
        print(f"重新手动处理: {filename}")
        manual_result = lines_detetor.detect_and_predict_vertical_lines(
            f"images/{filename}", mode='manual'
        )
        if manual_result:
            auto_results[filename] = manual_result
```

## 测试方法

运行测试脚本验证功能：

```bash
python test_manual_line_detection.py
```

测试脚本包含：
- 向后兼容性测试
- 自动模式测试
- 手动模式测试
- 无效模式测试
- 真实图像测试
- 交互式测试

## 注意事项

1. **图像窗口**：手动模式会弹出OpenCV窗口，确保显示环境支持
2. **鼠标操作**：需要鼠标进行点击操作
3. **选择精度**：手动选择时尽量点击准确的位置
4. **数量建议**：推荐选择15个点，但可以选择更少
5. **取消操作**：按ESC键可以随时取消选择

## 故障排除

### 窗口无法显示
- 检查是否在图形界面环境中运行
- 确保OpenCV正确安装并支持GUI

### 鼠标点击无响应
- 确保点击在图像窗口内
- 检查窗口是否获得焦点

### 选择结果不准确
- 重新运行手动模式
- 更仔细地选择垂直线位置
- 确保选择的是真正的垂直线特征

这个升级后的函数提供了更大的灵活性，既保持了原有的自动化优势，又在需要时提供了精确的手动控制能力。
