# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块
try:
    import xbot
    from xbot import print, sleep
    from .import package
    from .package import variables as glv
except:
    print('running on your system!!')
import cv2
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter

def detect_and_predict_vertical_lines(image_path, mode='auto', vertical_angle_tolerance=10, spacing_cluster_tolerance=10):
    """
    检测图片中的所有竖线，将检测到的线作为基准，智能地填充和延伸缺失的线条。

    参数:
    image_path (str): 输入图片的路径。
    mode (str): 检测模式，'auto' 为自动检测，'manual' 为手动选择。
    vertical_angle_tolerance (int): 竖线角度的容忍度（单位：度）。
    spacing_cluster_tolerance (int): 间距聚类时的容差（像素）。

    返回:
    list: 垂直线的x坐标列表
    """
    # 1. 读取并预处理图片
    try:
        original_image = cv2.imread(image_path)
        if original_image is None:
            print(f"错误：无法读取图片，请检查路径：{image_path}")
            return []
        img_height, img_width = original_image.shape[:2]
    except Exception as e:
        print(f"读取图片时发生错误：{e}")
        return []

    # 根据模式选择处理方式
    if mode == 'manual':
        return _manual_line_selection(original_image, image_path)
    elif mode == 'auto':
        return _auto_detect_lines(original_image, img_height, img_width,
                                vertical_angle_tolerance, spacing_cluster_tolerance)
    else:
        print(f"错误：不支持的模式 '{mode}'，请使用 'auto' 或 'manual'")
        return []

def _manual_line_selection(original_image, image_path):
    """
    手动选择垂直线位置

    参数:
    original_image: 原始图像
    image_path: 图像路径（用于显示标题）

    返回:
    list: 用户选择的垂直线x坐标列表
    """
    print("=== 手动选择模式 ===")
    print("请在图像上点击选择15个垂直线位置")
    print("点击完成后按任意键继续，按ESC键取消")

    # 全局变量存储点击的坐标
    clicked_points = []
    display_image = original_image.copy()

    def mouse_callback(event, x, y, flags, param):
        nonlocal clicked_points, display_image

        if event == cv2.EVENT_LBUTTONDOWN:
            if len(clicked_points) < 15:
                clicked_points.append(x)
                # 在图像上绘制垂直线
                cv2.line(display_image, (x, 0), (x, display_image.shape[0]), (0, 255, 0), 2)
                # 在顶部添加序号标记
                cv2.putText(display_image, str(len(clicked_points)), (x-10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

                print(f"已选择第 {len(clicked_points)} 个点: x={x}")

                if len(clicked_points) == 15:
                    print("已选择15个点，按任意键完成选择")

                # 更新显示
                cv2.imshow('Manual Line Selection', display_image)

    # 创建窗口并设置鼠标回调
    window_name = 'Manual Line Selection'
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 1200, 800)
    cv2.setMouseCallback(window_name, mouse_callback)

    # 显示图像
    cv2.imshow(window_name, display_image)

    # 等待用户操作
    while True:
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC键
            print("用户取消选择")
            clicked_points = []
            break
        elif key != 255 and len(clicked_points) > 0:  # 任意键且已有选择
            break
        elif len(clicked_points) == 15:  # 自动完成
            cv2.waitKey(1000)  # 等待1秒让用户看到结果
            break

    cv2.destroyAllWindows()

    # 处理选择结果
    if len(clicked_points) == 0:
        print("未选择任何点")
        return []
    elif len(clicked_points) < 15:
        print(f"警告：只选择了 {len(clicked_points)} 个点，少于推荐的15个点")
        user_input = input("是否继续使用这些点？(y/n): ").lower().strip()
        if user_input != 'y':
            print("用户选择重新开始")
            return []

    # 对坐标进行排序
    final_coords = sorted(clicked_points)
    print(f"手动选择完成，共选择 {len(final_coords)} 个垂直线位置:")
    print(final_coords)

    return final_coords

def _auto_detect_lines(original_image, img_height, img_width, vertical_angle_tolerance, spacing_cluster_tolerance):
    """
    自动检测垂直线（原有逻辑）

    参数:
    original_image: 原始图像
    img_height: 图像高度
    img_width: 图像宽度
    vertical_angle_tolerance: 垂直角度容忍度
    spacing_cluster_tolerance: 间距聚类容忍度

    返回:
    list: 检测到的垂直线x坐标列表
    """

    print("=== 自动检测模式 ===")

    gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)

    # 2. 二值化
    custom_threshold = 230
    max_value = 255
    _, binary_image = cv2.threshold(gray_image, custom_threshold, max_value, cv2.THRESH_BINARY)

    # # --- 3. 使用 Matplotlib 可视化展示 ---
    # # 创建一个 1x2 的图床
    # fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    # # 显示原始灰度图
    # axes[0].imshow(gray_image, cmap='gray')
    # axes[0].set_title('Original Grayscale Image')
    # axes[0].axis('off') # 关闭坐标轴

    # # 显示二值化后的图像
    # axes[1].imshow(binary_image, cmap='gray')
    # axes[1].axis('off') # 关闭坐标轴

    # # 调整布局并显示图像
    # plt.tight_layout()
    # plt.show()


    # 3. 边缘检测 (与原代码相同)
    edges = cv2.Canny(binary_image, 50, 150, apertureSize=3)

    # 4. 概率霍夫变换检测直线 (与原代码相同)
    # minLineLength=20, 点和点之间差多少就不认为是一条直线了 
    # maxLineGap=20， 短于多少线就不认为是一条直线了
    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=50, minLineLength=20, maxLineGap=20)

    # 5. 筛选竖线并存储坐标 (与原代码相同)
    vertical_lines = []
    if lines is not None:
        for line in lines:
            x1, y1, x2, y2 = line[0]
            if abs(x2 - x1) < vertical_angle_tolerance: # 更稳健的竖线判断
                vertical_lines.append({'coords': (x1, y1, x2, y2), 'x_center': (x1 + x2) / 2})

    if not vertical_lines:
        print("未检测到任何竖线。")
        return []

    # 按中心横坐标对检测到的竖线进行排序
    sorted_lines = sorted(vertical_lines, key=lambda item: item['x_center'])
    # 提取唯一的、已检测到的横坐标作为基准
    detected_x_coords = sorted(list(set([round(line['x_center']) for line in sorted_lines])))
    
    print("--- 检测到的基准线坐标 ---")
    print(detected_x_coords)

    if len(detected_x_coords) < 2:
        print("基准线不足两条，无法预测间距。")
        return detected_x_coords

    # 计算相邻基准线之间的间距
    spacings = np.diff(detected_x_coords)
    
    # 聚类找到最可能的间距 (与原代码相同)
    rounded_spacings = [round(s / spacing_cluster_tolerance) * spacing_cluster_tolerance for s in spacings]
    filtered_spacings = [s for s in rounded_spacings if 20 < s < 150] # 过滤不合理的间距
    if not filtered_spacings:
        print("没有有效的间距用于预测。")
        return detected_x_coords
        
    spacing_counts = Counter(filtered_spacings)
    most_common_spacing = spacing_counts.most_common(1)[0][0]
    
    print(f"\n--- 间距聚类结果 ---")
    print(f"  最常见的间距是: {most_common_spacing:.2f} 像素")

    # =========================================================================
    # ========== 算法升级：填充空隙 (Interpolate) 与 向外延伸 (Extrapolate) ==========
    # =========================================================================
    
    # 使用集合来存储新预测的线，避免重复
    predicted_lines = set()

    # --- 1. 填充内部空隙 (Interpolation) ---
    for i in range(len(detected_x_coords) - 1):
        start_x = detected_x_coords[i]
        end_x = detected_x_coords[i+1]
        gap = end_x - start_x
        
        # 计算这个空隙中可以容纳几条线
        num_missing = round(gap / most_common_spacing) - 1
        
        if num_missing > 0:
            print(f"在 {start_x} 和 {end_x} 之间检测到 {num_missing} 条缺失线。")
            for j in range(1, int(num_missing) + 1):
                new_x = int(round(start_x + j * most_common_spacing))
                predicted_lines.add(new_x)

    # --- 2. 向外延伸 (Extrapolation) ---
    # 向左延伸
    current_x = detected_x_coords[0]
    while current_x > 0:
        current_x -= most_common_spacing
        if current_x > 0:
            predicted_lines.add(int(round(current_x)))

    # 向右延伸
    current_x = detected_x_coords[-1]
    while current_x < img_width:
        current_x += most_common_spacing
        if current_x < img_width:
            predicted_lines.add(int(round(current_x)))
            
    # 从集合转换为排序列表
    newly_predicted_coords = sorted(list(predicted_lines))
    print("\n--- 新增的预测线坐标 ---")
    print(newly_predicted_coords)

    # 合并基准线和预测线，得到最终的完整列表
    all_lines_coords = sorted(detected_x_coords + newly_predicted_coords)


    # --- 可视化 ---
    output_image = original_image.copy()
    
    # 绘制原始检测到的基准线 (绿色)
    for x_coord in detected_x_coords:
        cv2.line(output_image, (x_coord, 0), (x_coord, img_height), (0, 255, 0), 2) # 绿色
        
    # 绘制新增的预测线 (红色)
    for x_coord in newly_predicted_coords:
        cv2.line(output_image, (x_coord, 0), (x_coord, img_height), (0, 0, 255), 2) # 红色

    plt.figure(figsize=(15, 10))
    plt.title('Fill & Extend Result (Detected in Green, Newly Predicted in Red)')
    plt.imshow(cv2.cvtColor(output_image, cv2.COLOR_BGR2RGB))
    plt.axis('off')
    plt.show()

    # 执行最后的坐标清洗
    spacing_diff = np.diff(all_lines_coords)
    final_lines_coor = []
    final_lines_coor.append(all_lines_coords[0])
    for i in range(len(spacing_diff)):
        if spacing_diff[i] > 20:
            final_lines_coor.append(all_lines_coords[i+1])
    print("\n--- 最终所有线的完整坐标 ---")
    print(final_lines_coor)
    return final_lines_coor


def ut_main():
    # 请将 'path/to/your/image.jpg' 替换为你的图片路径
    # 建议使用一张包含清晰、等间距竖线的图片进行测试
    image_file = r"C:\Users\<USER>\Documents\ths_gs_resource\template\cropped_kline_precessing_main_widnows.jpg"
    all_lines_coords = detect_and_predict_vertical_lines(
        image_file,
        vertical_angle_tolerance=5,   # 角度容忍度
        spacing_cluster_tolerance=5   # 间距聚类的容差，例如5像素内的间距被视为同一类
    )
    return all_lines_coords

def get_lines_coors():
    # 请将 'path/to/your/image.jpg' 替换为你的图片路径
    # 建议使用一张包含清晰、等间距竖线的图片进行测试
    image_file = r"C:\Users\<USER>\Documents\ths_gs_resource\img\60mins\PixPin_2025-08-17_11-29-30.jpg"
    all_lines_coords = detect_and_predict_vertical_lines(
        image_file,
        vertical_angle_tolerance=5,   # 角度容忍度
        spacing_cluster_tolerance=2   # 间距聚类的容差，例如5像素内的间距被视为同一类
    )
    return all_lines_coords


def main(args):
    pass

if __name__ == "__main__":
    ut_main()