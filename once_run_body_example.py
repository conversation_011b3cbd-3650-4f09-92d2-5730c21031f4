# once_run_body_example.py - once_run_body函数使用示例

"""
这个文件展示了如何正确使用 once_run_body() 函数
"""

import time
from logger_manager import log_info, log_error, log_success, log_warning

def example_usage():
    """展示once_run_body函数的正确使用方法"""
    
    print("=== once_run_body 函数使用示例 ===")
    
    # 1. 定义必要的回调函数
    def wait_if_paused():
        """暂停等待函数 - 在实际使用中会检查control对象"""
        # 在这个示例中，我们不需要实际暂停
        pass
    
    def should_halt():
        """停止检查函数 - 在实际使用中会检查control对象"""
        # 在这个示例中，我们不停止
        return False
    
    def clog(msg):
        """日志记录函数"""
        log_info(f"[CLOG] {msg}")
    
    # 2. 准备股票数据
    stock_examples = [
        ["000001", "平安银行", "60min"],
        ["000002", "万科A", "60min"],
        ["600036", "招商银行", "60min"],
    ]
    
    # 3. 模拟窗口对象（在实际使用中这应该是真实的窗口对象）
    ths_window = None  # 在实际使用中，这应该通过 open_ths_pyauto.connect_to_window() 获取
    
    print("\n函数调用示例:")
    print("```python")
    print("# 导入函数")
    print("from task_main import once_run_body")
    print("")
    print("# 调用函数")
    print("signal = once_run_body(")
    print("    ths_window=ths_window,")
    print("    stock=stock,")
    print("    wait_if_paused_func=wait_if_paused,")
    print("    should_halt_func=should_halt,")
    print("    clog_func=clog")
    print(")")
    print("```")
    
    # 4. 展示参数说明
    print("\n参数说明:")
    for i, stock in enumerate(stock_examples, 1):
        print(f"{i}. 股票数据示例: {stock}")
        print(f"   - 代码: {stock[0]}")
        print(f"   - 名称: {stock[1]}")
        print(f"   - 周期: {stock[2]}")
    
    print("\n回调函数说明:")
    print("- wait_if_paused_func: 检查暂停状态，如果暂停则等待")
    print("- should_halt_func: 检查是否应该停止，返回True/False")
    print("- clog_func: 记录日志信息，接收一个字符串参数")
    
    print("\n返回值说明:")
    print("函数返回 [信号值, 状态信息] 格式的列表")
    print("- signal[0]: 整数，0表示无信号，非0表示有信号")
    print("- signal[1]: 字符串，状态描述或错误信息")

def show_integration_example():
    """展示在main函数中的集成示例"""
    
    print("\n=== 在main函数中的集成示例 ===")
    
    integration_code = '''
def main(args=None, control=None):
    """主任务入口"""
    
    # 定义回调函数
    def wait_if_paused():
        if not control:
            return
        while control.is_paused and not control.should_stop:
            control.set_status("已暂停")
            time.sleep(0.2)

    def should_halt() -> bool:
        return bool(control and control.should_stop)

    def clog(msg: str):
        try:
            if control:
                control.log(msg)
            else:
                log_info(msg)
        except Exception:
            log_error(f"日志记录异常: {msg}")
    
    # 初始化同花顺窗口
    process = open_ths_pyauto.launch_tonghuashun_process()
    ths_window = open_ths_pyauto.connect_to_window(process)
    
    # 获取股票列表
    stock_list = get_stock_pool.fetch_stock_data(db_config, 'favorite_list')
    
    # 处理每只股票
    for index, stock_t in enumerate(stock_list):
        # 准备股票数据
        stock = [
            extract_stock_code(stock_t[0]),
            stock_t[1],
            "60min"
        ]
        
        # 检查停止信号
        if should_halt():
            break
        wait_if_paused()
        
        # 调用once_run_body处理股票
        signal = once_run_body(ths_window, stock, wait_if_paused, should_halt, clog)
        
        # 如果第一次无信号，重试一次
        if signal[0] == 0:
            clog(f"股票 {stock[0]} 第一次无信号，重试一次")
            time.sleep(1)
            signal = once_run_body(ths_window, stock, wait_if_paused, should_halt, clog)
        
        # 处理结果
        if signal[0] != 0:
            log_success(f"股票 {stock[0]} 检测到信号: {signal}")
        else:
            log_info(f"股票 {stock[0]} 无信号")
        
        # 更新进度
        if control:
            progress = int((index + 1) / len(stock_list) * 100)
            control.set_progress(progress)
'''
    
    print("集成代码示例:")
    print("```python")
    print(integration_code)
    print("```")

def show_error_handling():
    """展示错误处理示例"""
    
    print("\n=== 错误处理示例 ===")
    
    error_handling_code = '''
# 调用函数并处理可能的错误
try:
    signal = once_run_body(ths_window, stock, wait_if_paused, should_halt, clog)
    
    # 检查返回值
    if isinstance(signal, list) and len(signal) >= 2:
        signal_value = signal[0]
        status_info = signal[1]
        
        if signal_value != 0:
            log_success(f"检测到信号: {signal_value}")
        else:
            if "错误" in status_info or "失败" in status_info:
                log_error(f"处理失败: {status_info}")
            else:
                log_info(f"无信号: {status_info}")
    else:
        log_error(f"返回值格式异常: {signal}")
        
except Exception as e:
    log_error(f"调用once_run_body时发生异常: {e}")
'''
    
    print("错误处理代码:")
    print("```python")
    print(error_handling_code)
    print("```")
    
    print("\n常见错误类型:")
    print("1. 窗口对象无效 - 确保ths_window是有效的窗口对象")
    print("2. 股票数据格式错误 - 确保stock是包含3个元素的列表")
    print("3. 回调函数未定义 - 确保所有回调函数都正确实现")
    print("4. 截图失败 - 检查窗口是否可见和可访问")
    print("5. 配置文件缺失 - 确保resources/coor_config.json存在")

def main():
    """主函数"""
    log_info("开始once_run_body函数使用示例")
    
    example_usage()
    show_integration_example()
    show_error_handling()
    
    log_success("once_run_body函数使用示例完成")
    
    print("\n总结:")
    print("1. once_run_body函数需要5个参数：窗口对象、股票数据、3个回调函数")
    print("2. 函数返回[信号值, 状态信息]格式的结果")
    print("3. 包含完整的错误处理和日志记录")
    print("4. 可以在main函数中重复调用处理多只股票")
    print("5. 支持暂停/恢复和停止控制")

if __name__ == "__main__":
    main()
