"""
调试键盘输入问题的测试脚本
"""
import pyautogui
import time
from open_ths_pyauto import (
    launch_tonghuashun_process, 
    connect_to_window, 
    resize_and_move_window,
    ensure_window_focus,
    input_text_to_window,
    press_enter
)

def test_keyboard_input():
    """
    测试键盘输入功能
    """
    print("=== 键盘输入调试测试 ===")
    
    # 设置pyautogui
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    
    # 启动同花顺
    print("1. 启动同花顺软件...")
    process = launch_tonghuashun_process()
    if not process:
        print("启动失败")
        return False
    
    # 连接窗口
    print("2. 连接到窗口...")
    window = connect_to_window(process)
    if not window:
        print("连接窗口失败")
        return False
    
    # 调整窗口
    print("3. 调整窗口大小和位置...")
    resize_and_move_window(window, 0, 0, 1920, 1080)
    
    # 等待稳定
    print("4. 等待窗口稳定...")
    time.sleep(5)
    
    # 测试不同的输入方法
    print("5. 开始测试键盘输入...")
    
    # 方法1：直接使用pyautogui
    print("方法1：直接使用pyautogui输入...")
    ensure_window_focus(window)
    time.sleep(2)
    
    try:
        # 先点击窗口中心确保焦点
        rect = window.rectangle()
        center_x = rect.left + rect.width() // 2
        center_y = rect.top + rect.height() // 2
        print(f"点击窗口中心: ({center_x}, {center_y})")
        pyautogui.click(center_x, center_y)
        time.sleep(1)
        
        # 清空输入区域
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        # 输入35
        print("输入 '35'...")
        pyautogui.typewrite('35', interval=0.2)
        time.sleep(1)
        
        print("按回车...")
        pyautogui.press('enter')
        time.sleep(2)
        
        print("方法1完成")
        
    except Exception as e:
        print(f"方法1失败: {e}")
    
    # 方法2：使用封装的函数
    print("方法2：使用封装的输入函数...")
    time.sleep(2)
    
    try:
        success = input_text_to_window(window, '35')
        if success:
            press_enter()
            print("方法2完成")
        else:
            print("方法2输入失败")
    except Exception as e:
        print(f"方法2失败: {e}")
    
    # 方法3：逐个按键
    print("方法3：逐个按键输入...")
    time.sleep(2)
    
    try:
        ensure_window_focus(window)
        time.sleep(1)
        
        # 清空
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.3)
        pyautogui.press('delete')
        time.sleep(0.3)
        
        # 逐个按键
        pyautogui.press('3')
        time.sleep(0.3)
        pyautogui.press('5')
        time.sleep(0.3)
        pyautogui.press('enter')
        
        print("方法3完成")
        
    except Exception as e:
        print(f"方法3失败: {e}")
    
    print("=== 测试完成 ===")
    print("请观察同花顺窗口是否有输入内容")
    
    return True

def test_simple_input():
    """
    简单的输入测试（不启动同花顺）
    """
    print("=== 简单输入测试 ===")
    print("请在5秒内点击任意文本输入框（如记事本）...")
    
    for i in range(5, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("开始输入测试...")
    
    try:
        # 清空
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        # 输入测试文本
        pyautogui.typewrite('35', interval=0.2)
        time.sleep(1)
        pyautogui.press('enter')
        
        print("简单输入测试完成")
        
    except Exception as e:
        print(f"简单输入测试失败: {e}")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整测试（启动同花顺）")
    print("2. 简单输入测试（需要手动打开文本编辑器）")
    
    choice = input("请选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_keyboard_input()
    elif choice == "2":
        test_simple_input()
    else:
        print("无效选择")
