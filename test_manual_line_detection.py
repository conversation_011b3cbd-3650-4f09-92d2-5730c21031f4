# test_manual_line_detection.py - 测试手动线条检测功能
import cv2
import numpy as np
import os
from logger_manager import log_info, log_success, log_warning, log_error

def create_test_image():
    """创建一个测试图像，包含一些垂直线"""
    # 创建一个白色背景的图像
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 添加一些垂直线作为测试
    line_positions = [100, 150, 200, 280, 350, 420, 500, 580, 650, 720]
    
    for x in line_positions:
        # 绘制黑色垂直线
        cv2.line(img, (x, 50), (x, 550), (0, 0, 0), 2)
        # 添加一些噪声线条
        cv2.line(img, (x-1, 100), (x-1, 400), (128, 128, 128), 1)
        cv2.line(img, (x+1, 150), (x+1, 450), (128, 128, 128), 1)
    
    # 添加一些水平线作为干扰
    for y in [100, 200, 300, 400, 500]:
        cv2.line(img, (50, y), (750, y), (200, 200, 200), 1)
    
    # 保存测试图像
    test_image_path = "test_vertical_lines.png"
    cv2.imwrite(test_image_path, img)
    log_success(f"测试图像已创建: {test_image_path}")
    
    return test_image_path

def test_auto_mode():
    """测试自动检测模式"""
    log_info("=== 测试自动检测模式 ===")
    
    # 创建测试图像
    test_image_path = create_test_image()
    
    try:
        # 导入lines_detetor模块
        import lines_detetor
        
        # 测试自动模式
        result = lines_detetor.detect_and_predict_vertical_lines(
            test_image_path, 
            mode='auto',
            vertical_angle_tolerance=5,
            spacing_cluster_tolerance=5
        )
        
        if result:
            log_success(f"自动检测成功，检测到 {len(result)} 条垂直线")
            log_info(f"检测结果: {result}")
        else:
            log_warning("自动检测未找到垂直线")
            
    except Exception as e:
        log_error(f"自动检测测试失败: {e}")
    
    # 清理测试文件
    if os.path.exists(test_image_path):
        os.remove(test_image_path)

def test_manual_mode():
    """测试手动选择模式"""
    log_info("=== 测试手动选择模式 ===")
    
    # 创建测试图像
    test_image_path = create_test_image()
    
    try:
        # 导入lines_detetor模块
        import lines_detetor
        
        log_info("即将启动手动选择模式...")
        log_info("请在弹出的窗口中点击选择垂直线位置")
        log_info("建议选择15个位置，完成后按任意键确认")
        
        # 测试手动模式
        result = lines_detetor.detect_and_predict_vertical_lines(
            test_image_path, 
            mode='manual'
        )
        
        if result:
            log_success(f"手动选择成功，选择了 {len(result)} 条垂直线")
            log_info(f"选择结果: {result}")
        else:
            log_warning("手动选择被取消或未选择任何线条")
            
    except Exception as e:
        log_error(f"手动选择测试失败: {e}")
    
    # 清理测试文件
    if os.path.exists(test_image_path):
        os.remove(test_image_path)

def test_invalid_mode():
    """测试无效模式"""
    log_info("=== 测试无效模式 ===")
    
    test_image_path = create_test_image()
    
    try:
        import lines_detetor
        
        # 测试无效模式
        result = lines_detetor.detect_and_predict_vertical_lines(
            test_image_path, 
            mode='invalid_mode'
        )
        
        if not result:
            log_success("无效模式正确返回空列表")
        else:
            log_warning(f"无效模式意外返回结果: {result}")
            
    except Exception as e:
        log_error(f"无效模式测试失败: {e}")
    
    # 清理测试文件
    if os.path.exists(test_image_path):
        os.remove(test_image_path)

def test_backward_compatibility():
    """测试向后兼容性"""
    log_info("=== 测试向后兼容性 ===")
    
    test_image_path = create_test_image()
    
    try:
        import lines_detetor
        
        # 测试不指定mode参数（应该默认为auto）
        result = lines_detetor.detect_and_predict_vertical_lines(
            test_image_path,
            vertical_angle_tolerance=5,
            spacing_cluster_tolerance=5
        )
        
        if result:
            log_success("向后兼容性测试通过，默认使用自动模式")
            log_info(f"检测结果: {result}")
        else:
            log_warning("向后兼容性测试：未检测到垂直线")
            
    except Exception as e:
        log_error(f"向后兼容性测试失败: {e}")
    
    # 清理测试文件
    if os.path.exists(test_image_path):
        os.remove(test_image_path)

def test_with_real_image():
    """使用真实图像进行测试"""
    log_info("=== 使用真实图像测试 ===")
    
    # 查找可能的真实图像文件
    possible_paths = [
        "resources/cache/img/60min",
        "resources/cache/debug",
        "."
    ]
    
    real_image_path = None
    for path in possible_paths:
        if os.path.exists(path):
            for file in os.listdir(path):
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    real_image_path = os.path.join(path, file)
                    break
            if real_image_path:
                break
    
    if not real_image_path:
        log_warning("未找到真实图像文件，跳过真实图像测试")
        return
    
    log_info(f"使用真实图像: {real_image_path}")
    
    try:
        import lines_detetor
        
        # 测试自动模式
        log_info("测试自动模式...")
        auto_result = lines_detetor.detect_and_predict_vertical_lines(
            real_image_path, 
            mode='auto'
        )
        
        if auto_result:
            log_success(f"自动模式检测到 {len(auto_result)} 条线")
        else:
            log_warning("自动模式未检测到线条")
        
        # 询问是否测试手动模式
        user_input = input("是否测试手动模式？(y/n): ").lower().strip()
        if user_input == 'y':
            log_info("启动手动模式...")
            manual_result = lines_detetor.detect_and_predict_vertical_lines(
                real_image_path, 
                mode='manual'
            )
            
            if manual_result:
                log_success(f"手动模式选择了 {len(manual_result)} 条线")
                
                # 比较两种模式的结果
                if auto_result and manual_result:
                    log_info("模式比较:")
                    log_info(f"自动模式: {len(auto_result)} 条线")
                    log_info(f"手动模式: {len(manual_result)} 条线")
            else:
                log_info("手动模式被取消")
        
    except Exception as e:
        log_error(f"真实图像测试失败: {e}")

def interactive_test():
    """交互式测试"""
    log_info("=== 交互式测试 ===")
    
    while True:
        print("\n请选择测试模式:")
        print("1. 自动检测模式")
        print("2. 手动选择模式") 
        print("3. 使用真实图像测试")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            test_auto_mode()
        elif choice == '2':
            test_manual_mode()
        elif choice == '3':
            test_with_real_image()
        elif choice == '4':
            log_info("退出交互式测试")
            break
        else:
            log_warning("无效选择，请重新输入")

def main():
    """主测试函数"""
    log_info("开始测试手动线条检测功能")
    log_info("=" * 60)
    
    # 运行基本测试
    test_backward_compatibility()
    test_invalid_mode()
    test_auto_mode()
    
    # 询问是否进行手动测试
    user_input = input("\n是否进行手动选择测试？这将弹出图像窗口 (y/n): ").lower().strip()
    if user_input == 'y':
        test_manual_mode()
    
    # 询问是否进行交互式测试
    user_input = input("\n是否进行交互式测试？(y/n): ").lower().strip()
    if user_input == 'y':
        interactive_test()
    
    log_success("线条检测功能测试完成")
    log_info("=" * 60)
    
    print("\n功能说明:")
    print("1. mode='auto': 使用原有的自动检测算法")
    print("2. mode='manual': 启用手动点击选择模式")
    print("3. 默认mode='auto'，保持向后兼容")
    print("4. 手动模式下点击图像选择垂直线位置")
    print("5. 建议选择15个位置以获得最佳效果")

if __name__ == "__main__":
    main()
