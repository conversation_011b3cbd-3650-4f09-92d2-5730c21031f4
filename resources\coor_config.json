{"main_kline_capture_coor": {"p1": [97, 122], "p2": [1231, 545]}, "line_x": [45, 120, 195, 272, 345, 421, 497, 571, 645, 720, 795, 871, 945, 1020, 1095], "line_width": 31, "lines_range": [[14, 76], [89, 151], [164, 226], [241, 303], [314, 376], [390, 452], [466, 528], [540, 602], [614, 676], [689, 751], [764, 826], [840, 902], [914, 976], [989, 1051], [1064, 1126]], "window": {"windows_size": [1920, 1080], "search_box_coor": [1920, 1980]}, "sql_config_info": {"host": "**************", "user": "ken", "password": "li173312", "database": "ths_stock", "charset": "utf8mb4", "cursorclass": "pymysql.cursors.Cursor"}, "debug_config": {"enable_debug_mode": true, "save_intermediate_images": true, "debug_output_dir": "resources/cache/debug", "save_original_screenshot": true, "save_cropped_image": true, "save_detection_result": true, "image_format": "jpg", "add_timestamp_to_filename": true}, "interactive_capture": {"last_screenshot_path": "resources\\images\\interactive_screenshot_20250824_000102.png", "original_selection": {"p1": [97, 122], "p2": [1231, 545]}, "kline_centers": [45, 120, 195, 272, 345, 421, 497, 571, 645, 720, 795, 871, 945, 1020, 1095], "kline_count": 15, "timestamp": "2025-08-24 00:01:33"}}