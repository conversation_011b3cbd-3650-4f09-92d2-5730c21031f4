# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块
try:
    import xbot
    from xbot import print, sleep
    from .import package
    from .package import variables as glv
except:
    print("runing on your personal PC")
import pymysql
from pymysql.cursors import DictCursor

def main(args):
    # PyMySQL 的连接配置字典与 mysql.connector 兼容
    db_connection_config = {
        'host': '**************',
        'user': 'ken',
        'password': 'li173312',
        'database': 'ths_stock',
        'charset': 'utf8mb4', # 建议总是指定字符集
        'cursorclass': pymysql.cursors.Cursor # 使用标准游标
    }
    data = [
        ["000001", "平安", 1, 0, None, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
    ]
    
    total_field_count = 30
    is_data_valid = all(len(row) == total_field_count for row in data)
    if not is_data_valid:
        print("错误：示例数据长度不正确，请检查！")
    else:
        upsert_stock_data(data)

class DatabaseManager:
    """
    一个简单的数据库连接上下文管理器。
    - 初始化时接收数据库配置。
    - 使用 'with' 语句可以自动连接、提交/回滚和关闭连接。
    """
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None
        self.cursor = None

    def __enter__(self):
        """当进入 'with' 语句块时调用，建立连接并返回游标。"""
        try:
            self.conn = pymysql.connect(**self.db_config)
            self.cursor = self.conn.cursor()
            return self.cursor
        except pymysql.MySQLError as e:
            print(f"数据库连接失败: {e}")
            raise # 抛出异常，防止 with 块内的代码执行

    def __exit__(self, exc_type, exc_val, exc_tb):
        """当退出 'with' 语句块时调用，处理事务和关闭连接。"""
        if exc_type:  # 如果 'with' 块内发生了异常
            print(f"发生异常，事务将回滚: {exc_val}")
            self.conn.rollback()
        else:  # 如果 'with' 块正常结束
            self.conn.commit()
            print("事务已成功提交。")
        
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("数据库连接已关闭。")
        # 返回 False 表示如果发生了异常，异常应该被正常抛出
        return False

def upsert_stock_data(data_to_write):
    """
    将列表格式的股票数据写入数据库 (使用 PyMySQL 和自定义连接管理器)。
    """
    db_config = {
        'host': '**************',
        'user': 'ken',
        'password': 'li173312',
        'database': 'ths_stock',
        'charset': 'utf8mb4', # 建议总是指定字符集
        'cursorclass': pymysql.cursors.Cursor # 使用标准游标
    }
    all_required_fields = [
        'code', 'name', 
        'LD_flag_-14', 'LD_flag_-13', 'LD_flag_-12', 'LD_flag_-11', 'LD_flag_-10',
        'LD_flag_-9', 'LD_flag_-8', 'LD_flag_-7', 'LD_flag_-6', 'LD_flag_-5',
        'LD_flag_-4', 'LD_flag_-3', 'LD_flag_-2', 'LD_flag_-1',
        'LM_flag_-14', 'LM_flag_-13', 'LM_flag_-12', 'LM_flag_-11', 'LM_flag_-10',
        'LM_flag_-9', 'LM_flag_-8', 'LM_flag_-7', 'LM_flag_-6', 'LM_flag_-5',
        'LM_flag_-4', 'LM_flag_-3', 'LM_flag_-2', 'LM_flag_-1'
    ]
    
    table_name = 'status_signal'
    
    expected_length = len(all_required_fields)
    for i, row in enumerate(data_to_write):
        if len(row) != expected_length:
            print(f"错误: 第 {i+1} 行数据长度为 {len(row)}，但期望长度为 {expected_length}。程序终止。")
            return

    try:
        # 使用我们自定义的连接管理器
        db_manager = DatabaseManager(db_config)
        with db_manager as cursor:
            # 1. 检查并同步表结构
            print("--- 步骤 1: 检查并同步表结构 ---")
            cursor.execute(f"SHOW COLUMNS FROM {table_name};")
            # PyMySQL 的 cursor.fetchall() 返回的是元组的元组
            existing_fields = {row[0] for row in cursor.fetchall()}
            
            missing_fields = set(all_required_fields) - existing_fields
            
            if missing_fields:
                print(f"发现缺失的字段: {', '.join(missing_fields)}")
                for field in missing_fields:
                    if 'flag' in field:
                        add_column_query = f"ALTER TABLE {table_name} ADD COLUMN `{field}` INT NULL DEFAULT NULL;"
                        print(f"执行: {add_column_query}")
                        cursor.execute(add_column_query)
                print("表结构同步完成。")
            else:
                print("表结构已是最新，无需修改。")

            # 2. 准备并执行数据写入
            print("\n--- 步骤 2: 准备并执行数据写入 ---")
            sql_data_tuples = [tuple(row) for row in data_to_write]
            
            fields_str = ', '.join([f"`{f}`" for f in all_required_fields])
            placeholders_str = ', '.join(['%s'] * len(all_required_fields))
            
            update_clause_parts = []
            for field in all_required_fields:
                if field != 'code':
                    update_clause_parts.append(f"`{field}` = VALUES(`{field}`)")
            update_clause_str = ', '.join(update_clause_parts)

            sql_query = (
                f"INSERT INTO {table_name} ({fields_str}) "
                f"VALUES ({placeholders_str}) "
                f"ON DUPLICATE KEY UPDATE {update_clause_str};"
            )
            
            print("将要执行的 SQL 模板:")
            print(sql_query)
            
            # 使用 executemany 高效地批量执行
            # PyMySQL 的 executemany 返回受影响的行数
            rows_affected = cursor.executemany(sql_query, sql_data_tuples)
            
            print(f"\n成功！{rows_affected} 条记录被插入或更新。")

    except pymysql.MySQLError as e:
        # 捕获在 'with' 块内部可能发生的具体数据库操作错误
        print(f"数据库操作执行失败: {e}")
    except Exception as e:
        # 捕获其他可能的错误，例如连接失败
        print(f"发生未知错误: {e}")


# --- 使用示例 (与之前完全相同) ---
if __name__ == "__main__":
    # PyMySQL 的连接配置字典与 mysql.connector 兼容
    db_connection_config = {
        'host': 'localhost',
        'user': 'your_username',
        'password': 'your_password',
        'database': 'your_database',
        'charset': 'utf8mb4', # 建议总是指定字符集
        'cursorclass': pymysql.cursors.Cursor # 使用标准游标
    }

    data = [
        ["000001", "平安银行", 1, 0, None, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
        ["000002", "万科A",   0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
        ["000001", "平安银行(新)", 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    ]
    
    total_field_count = 30
    is_data_valid = all(len(row) == total_field_count + 2 for row in data)
    if not is_data_valid:
        print("错误：示例数据长度不正确，请检查！")
    else:
        upsert_stock_data(db_connection_config, data)