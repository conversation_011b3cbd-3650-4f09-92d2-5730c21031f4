#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式屏幕截图功能测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from windows_precessing import interactive_screenshot_capture, run_interactive_mode
    print("成功导入交互式截图模块")
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

def test_interactive_screenshot():
    """测试交互式截图功能"""
    print("=" * 50)
    print("交互式屏幕截图功能测试")
    print("=" * 50)
    print("测试说明：")
    print("1. 程序将显示全屏半透明覆盖层")
    print("2. 请用鼠标拖拽选择一个矩形区域")
    print("3. 按回车键确认选择")
    print("4. 程序将自动截图并进行K线选择")
    print("5. 按ESC键可以取消选择")
    print()
    
    input("按回车键开始测试...")
    
    try:
        result = interactive_screenshot_capture()
        if result:
            print(f"测试成功！截图已保存到: {result}")
        else:
            print("测试失败或用户取消了操作")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

def test_dependencies():
    """测试依赖库"""
    print("检查依赖库...")
    
    dependencies = [
        ('tkinter', 'tkinter'),
        ('pyautogui', 'pyautogui'),
        ('keyboard', 'keyboard'),
        ('PIL', 'PIL'),
        ('cv2', 'cv2'),
        ('numpy', 'numpy')
    ]
    
    missing_deps = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✓ {name} - 已安装")
        except ImportError:
            print(f"✗ {name} - 未安装")
            missing_deps.append(name)
    
    if missing_deps:
        print(f"\n缺少以下依赖库: {', '.join(missing_deps)}")
        print("请使用以下命令安装:")
        for dep in missing_deps:
            if dep == 'PIL':
                print(f"pip install Pillow")
            elif dep == 'cv2':
                print(f"pip install opencv-python")
            else:
                print(f"pip install {dep}")
        return False
    else:
        print("\n所有依赖库都已安装！")
        return True

def main():
    """主函数"""
    print("交互式屏幕截图功能测试工具")
    print("=" * 40)
    
    # 检查依赖
    if not test_dependencies():
        print("请先安装缺少的依赖库")
        return
    
    print("\n选择测试模式：")
    print("1. 测试交互式截图功能")
    print("2. 启动完整的交互模式")
    print("3. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == '1':
                test_interactive_screenshot()
                break
            elif choice == '2':
                run_interactive_mode()
                break
            elif choice == '3':
                print("退出测试")
                break
            else:
                print("无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
