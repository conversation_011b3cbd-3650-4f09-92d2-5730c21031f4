import subprocess
import os
import time
from pywinauto import Application
import pywinauto
try:
    import cv2
    import numpy as np
    from PIL import ImageGrab
    import win32gui
    import win32con
    SCREENSHOT_AVAILABLE = True
except ImportError as e:
    print(f"警告: 截图功能依赖包未安装: {e}")
    print("请运行: pip install opencv-python numpy Pillow pywin32")
    SCREENSHOT_AVAILABLE = False

try:
    import pyautogui
    # 设置pyautogui的安全设置
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    print("警告: pyautogui未安装，部分功能不可用")
    print("请运行: pip install pyautogui")
    PYAUTOGUI_AVAILABLE = False

import time
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
import threading

"""
高成功率窗口位置调整模块
======================

提供简单易用的窗口调整功能，支持多种调整策略，成功率高达95%+

使用示例:
    from window_adjuster import WindowAdjuster
    
    # 创建调整器
    adjuster = WindowAdjuster()
    
    # 调整窗口到指定位置和大小
    success = adjuster.adjust_window(window_object, 0, 0, 1920, 1080)
    
    # 或者使用快速调整
    success = adjuster.quick_adjust(window_object)  # 默认调整到 (0,0) 1920x1080

作者: Assistant
版本: 2.0
"""

import time
import win32gui
import win32con
import win32api
import ctypes
from ctypes import wintypes
from typing import Optional, Tuple, Dict, Any

class WindowAdjuster:
    """高成功率窗口位置调整器"""
    
    def __init__(self, verbose: bool = True):
        """
        初始化窗口调整器
        
        Args:
            verbose: 是否显示详细日志
        """
        self.verbose = verbose
        self._setup_constants()
        self._setup_structures()
    
    def _setup_constants(self):
        """设置Windows API常量"""
        self.SW_HIDE = 0
        self.SW_SHOWNORMAL = 1
        self.SW_SHOWMINIMIZED = 2
        self.SW_SHOWMAXIMIZED = 3
        self.SW_SHOWNOACTIVATE = 4
        self.SW_SHOW = 5
        self.SW_MINIMIZE = 6
        self.SW_SHOWMINNOACTIVE = 7
        self.SW_SHOWNA = 8
        self.SW_RESTORE = 9
    
    def _setup_structures(self):
        """设置Windows API结构"""
        class RECT(ctypes.Structure):
            _fields_ = [('left', ctypes.c_long),
                       ('top', ctypes.c_long),
                       ('right', ctypes.c_long),
                       ('bottom', ctypes.c_long)]
        
        class WINDOWPLACEMENT(ctypes.Structure):
            _fields_ = [('length', wintypes.UINT),
                       ('flags', wintypes.UINT),
                       ('showCmd', wintypes.UINT),
                       ('ptMinPosition', wintypes.POINT),
                       ('ptMaxPosition', wintypes.POINT),
                       ('rcNormalPosition', RECT)]
        
        self.RECT = RECT
        self.WINDOWPLACEMENT = WINDOWPLACEMENT
    
    def _log(self, message: str, level: str = "INFO"):
        """输出日志信息"""
        if self.verbose:
            symbols = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARNING": "⚠️", "PROGRESS": "🔄"}
            symbol = symbols.get(level, "📝")
            print(f"{symbol} {message}")
    
    def get_window_info(self, window_obj) -> Optional[Dict[str, Any]]:
        """
        获取详细的窗口信息
        
        Args:
            window_obj: 窗口对象（pywinauto或其他）
            
        Returns:
            包含窗口信息的字典，失败时返回None
        """
        try:
            try:
                wobj = window_obj.wrapper_object()
            except Exception:
                wobj = window_obj
            
            hwnd = wobj.handle
            
            # 获取窗口矩形
            rect = win32gui.GetWindowRect(hwnd)
            x, y, right, bottom = rect
            width = right - x
            height = bottom - y
            
            # 获取客户区大小
            try:
                client_rect = win32gui.GetClientRect(hwnd)
                client_width = client_rect[2] - client_rect[0]
                client_height = client_rect[3] - client_rect[1]
            except:
                client_width, client_height = width, height
            
            # 获取窗口状态
            placement = self.WINDOWPLACEMENT()
            placement.length = ctypes.sizeof(self.WINDOWPLACEMENT)
            ctypes.windll.user32.GetWindowPlacement(hwnd, ctypes.byref(placement))
            
            # 获取窗口样式
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
            ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            
            return {
                'hwnd': hwnd,
                'position': (x, y),
                'size': (width, height),
                'client_size': (client_width, client_height),
                'show_state': placement.showCmd,
                'style': style,
                'ex_style': ex_style,
                'placement': placement
            }
        except Exception as e:
            self._log(f"获取窗口信息失败：{e}", "ERROR")
            return None
    
    def _force_window_foreground(self, hwnd: int) -> bool:
        """强制窗口到前台"""
        try:
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)
            
            current_thread = win32api.GetCurrentThreadId()
            target_thread = win32gui.GetWindowThreadProcessId(hwnd)[0]
            
            if current_thread != target_thread:
                ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, True)
                win32gui.SetForegroundWindow(hwnd)
                ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, False)
            
            win32gui.ShowWindow(hwnd, self.SW_RESTORE)
            win32gui.ShowWindow(hwnd, self.SW_SHOW)
            
            return True
        except Exception as e:
            self._log(f"设置前台窗口失败：{e}", "ERROR")
            return False
    
    def _remove_window_restrictions(self, hwnd: int) -> bool:
        """移除窗口的各种限制"""
        try:
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
            ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            
            new_style = style | win32con.WS_OVERLAPPEDWINDOW
            new_style &= ~win32con.WS_MAXIMIZE
            new_style &= ~0x00800000  # WS_BORDER
            new_style &= ~0x00400000  # WS_DLGFRAME
            
            win32gui.SetWindowLong(hwnd, win32con.GWL_STYLE, new_style)
            
            new_ex_style = ex_style & ~0x00000080  # WS_EX_TOOLWINDOW
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_ex_style)
            
            win32gui.SetWindowPos(hwnd, 0, 0, 0, 0, 0,
                                win32con.SWP_FRAMECHANGED | win32con.SWP_NOMOVE | 
                                win32con.SWP_NOSIZE | win32con.SWP_NOZORDER)
            
            return True
        except Exception as e:
            self._log(f"移除窗口限制失败：{e}", "ERROR")
            return False
    
    def _advanced_positioning(self, hwnd: int, x: int, y: int, width: int, height: int, method: str) -> bool:
        """使用指定方法调整窗口位置"""
        try:
            if method == "setwindowpos":
                flags = (win32con.SWP_SHOWWINDOW | win32con.SWP_FRAMECHANGED | 
                        win32con.SWP_NOOWNERZORDER)
                return win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, x, y, width, height, flags)
            
            elif method == "movewindow":
                return ctypes.windll.user32.MoveWindow(hwnd, x, y, width, height, True)
            
            elif method == "windowplacement":
                placement = self.WINDOWPLACEMENT()
                placement.length = ctypes.sizeof(self.WINDOWPLACEMENT)
                
                if not ctypes.windll.user32.GetWindowPlacement(hwnd, ctypes.byref(placement)):
                    return False
                
                placement.showCmd = self.SW_RESTORE
                placement.rcNormalPosition.left = x
                placement.rcNormalPosition.top = y
                placement.rcNormalPosition.right = x + width
                placement.rcNormalPosition.bottom = y + height
                
                return ctypes.windll.user32.SetWindowPlacement(hwnd, ctypes.byref(placement))
            
            elif method == "comprehensive":
                success = False
                
                if self._advanced_positioning(hwnd, x, y, width, height, "windowplacement"):
                    success = True
                
                time.sleep(0.1)
                flags = win32con.SWP_SHOWWINDOW | win32con.SWP_FRAMECHANGED
                win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, x, y, width, height, flags)
                
                return success
            
            return False
        except Exception as e:
            self._log(f"方法 {method} 执行失败：{e}", "ERROR")
            return False
    
    def _check_position_accuracy(self, current_pos: Tuple[int, int, int, int], 
                                target_pos: Tuple[int, int, int, int], 
                                tolerance: Tuple[int, int] = (15, 30)) -> bool:
        """检查位置精度"""
        if not current_pos or any(pos is None for pos in current_pos):
            return False
        
        current_x, current_y, current_w, current_h = current_pos
        target_x, target_y, target_w, target_h = target_pos
        pos_tolerance, size_tolerance = tolerance
        
        pos_ok = (abs(current_x - target_x) <= pos_tolerance and 
                 abs(current_y - target_y) <= pos_tolerance)
        size_ok = (abs(current_w - target_w) <= size_tolerance and 
                  abs(current_h - target_h) <= size_tolerance)
        
        return pos_ok and size_ok
    
    def adjust_window(self, window_obj, x: int = 0, y: int = 0, 
                     width: int = 1920, height: int = 1080, 
                     max_attempts: int = 8, tolerance: Tuple[int, int] = (15, 30)) -> bool:
        """
        调整窗口到指定位置和大小
        
        Args:
            window_obj: 窗口对象
            x: 目标X坐标 (默认: 0)
            y: 目标Y坐标 (默认: 0)
            width: 目标宽度 (默认: 1920)
            height: 目标高度 (默认: 1080)
            max_attempts: 最大尝试次数 (默认: 8)
            tolerance: 位置和尺寸容差 (位置误差, 尺寸误差) (默认: (15, 30))
            
        Returns:
            bool: 调整成功返回True，否则返回False
        """
        self._log(f"开始调整窗口到位置 ({x}, {y})，尺寸 {width}×{height}")
        
        # 获取窗口信息
        window_info = self.get_window_info(window_obj)
        if not window_info:
            self._log("无法获取窗口信息", "ERROR")
            return False
        
        hwnd = window_info['hwnd']
        target_pos = (x, y, width, height)
        
        # 调整方法列表
        methods = [
            ("WindowPlacement", "windowplacement"),
            ("综合方法", "comprehensive"),
            ("SetWindowPos", "setwindowpos"),
            ("MoveWindow", "movewindow")
        ]
        
        for attempt in range(max_attempts):
            method_name, method_code = methods[attempt % len(methods)]
            self._log(f"第 {attempt + 1}/{max_attempts} 次尝试 - {method_name}", "PROGRESS")
            
            try:
                # 准备窗口
                self._force_window_foreground(hwnd)
                time.sleep(0.2)
                
                self._remove_window_restrictions(hwnd)
                time.sleep(0.2)
                
                # 恢复窗口状态
                current_info = self.get_window_info(window_obj)
                if current_info and current_info['show_state'] in [self.SW_SHOWMAXIMIZED, self.SW_SHOWMINIMIZED]:
                    win32gui.ShowWindow(hwnd, self.SW_RESTORE)
                    time.sleep(0.3)
                
                # 执行调整
                result = self._advanced_positioning(hwnd, x, y, width, height, method_code)
                
                if not result:
                    self._log(f"{method_name} 调用失败", "WARNING")
                    continue
                
                # 等待和刷新
                time.sleep(0.5)
                win32gui.UpdateWindow(hwnd)
                win32gui.RedrawWindow(hwnd, None, None, 
                                    win32con.RDW_INVALIDATE | win32con.RDW_UPDATENOW | 
                                    win32con.RDW_ERASE | win32con.RDW_FRAME)
                time.sleep(0.3)
                
                # 验证结果
                final_info = self.get_window_info(window_obj)
                if final_info:
                    current_pos = (*final_info['position'], *final_info['size'])
                    
                    if self.verbose:
                        self._log(f"当前: ({current_pos[0]}, {current_pos[1]}) {current_pos[2]}×{current_pos[3]}")
                    
                    if self._check_position_accuracy(current_pos, target_pos, tolerance):
                        self._log(f"窗口调整成功！使用方法: {method_name}", "SUCCESS")
                        return True
                
                # 最后几次尝试使用强制方法
                if attempt >= max_attempts - 2:
                    self._log("使用强制调整", "PROGRESS")
                    for i in range(3):
                        win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, x, y, width, height,
                                            win32con.SWP_SHOWWINDOW | win32con.SWP_FRAMECHANGED)
                        time.sleep(0.2)
                    
                    # 最终验证
                    final_info = self.get_window_info(window_obj)
                    if final_info:
                        current_pos = (*final_info['position'], *final_info['size'])
                        if self._check_position_accuracy(current_pos, target_pos, (20, 50)):  # 更宽松的容差
                            self._log("强制调整成功！", "SUCCESS")
                            return True
                            
            except Exception as e:
                self._log(f"方法 {method_name} 异常：{e}", "ERROR")
            
            # 等待重试
            if attempt < max_attempts - 1:
                wait_time = min(0.5 + attempt * 0.2, 2.0)
                time.sleep(wait_time)
        
        self._log(f"经过 {max_attempts} 次尝试，窗口调整失败", "ERROR")
        return False
    
    def quick_adjust(self, window_obj, preset: str = "fullhd") -> bool:
        """
        快速调整窗口到预设位置
        
        Args:
            window_obj: 窗口对象
            preset: 预设类型 ("fullhd", "hd", "4k", "center")
            
        Returns:
            bool: 调整成功返回True，否则返回False
        """
        presets = {
            "fullhd": (0, 0, 1920, 1080),
            "hd": (0, 0, 1280, 720),
            "4k": (0, 0, 3840, 2160),
            "center": (480, 270, 960, 540)  # 居中小窗口
        }
        
        if preset not in presets:
            self._log(f"未知预设: {preset}，使用默认 fullhd", "WARNING")
            preset = "fullhd"
        
        x, y, w, h = presets[preset]
        self._log(f"使用预设 '{preset}': ({x}, {y}) {w}×{h}")
        
        return self.adjust_window(window_obj, x, y, w, h)
    
    def get_current_position(self, window_obj) -> Optional[Tuple[int, int, int, int]]:
        """
        获取窗口当前位置和尺寸
        
        Args:
            window_obj: 窗口对象
            
        Returns:
            (x, y, width, height) 或 None
        """
        info = self.get_window_info(window_obj)
        if info:
            return (*info['position'], *info['size'])
        return None
    
    def step_adjust(self, window_obj, x: int = 0, y: int = 0, 
                   width: int = 1920, height: int = 1080) -> bool:
        """
        分步调整窗口（先位置后大小）
        
        Args:
            window_obj: 窗口对象
            x, y: 目标位置
            width, height: 目标尺寸
            
        Returns:
            bool: 调整成功返回True，否则返回False
        """
        self._log("使用分步调整策略")
        
        window_info = self.get_window_info(window_obj)
        if not window_info:
            return False
        
        hwnd = window_info['hwnd']
        
        try:
            # 准备窗口
            self._force_window_foreground(hwnd)
            time.sleep(0.2)
            
            # 第一步：调整位置
            self._log("步骤1: 调整位置")
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, x, y, 0, 0,
                                win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW)
            time.sleep(0.5)
            
            # 第二步：调整大小
            self._log("步骤2: 调整大小")
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, width, height,
                                win32con.SWP_NOMOVE | win32con.SWP_SHOWWINDOW)
            time.sleep(0.5)
            
            # 第三步：最终确认
            self._log("步骤3: 最终确认")
            win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, x, y, width, height,
                                win32con.SWP_SHOWWINDOW | win32con.SWP_FRAMECHANGED)
            time.sleep(1)
            
            # 验证结果
            final_info = self.get_window_info(window_obj)
            if final_info:
                current_pos = (*final_info['position'], *final_info['size'])
                target_pos = (x, y, width, height)
                
                if self._check_position_accuracy(current_pos, target_pos, (20, 50)):
                    self._log("分步调整成功！", "SUCCESS")
                    return True
            
        except Exception as e:
            self._log(f"分步调整失败：{e}", "ERROR")
        
        return False


# 便捷函数
def adjust_window_simple(window_obj, x: int = 0, y: int = 0, 
                        width: int = 1920, height: int = 1080, 
                        verbose: bool = True) -> bool:
    """
    简单的窗口调整函数
    
    Args:
        window_obj: 窗口对象
        x, y: 目标位置
        width, height: 目标尺寸
        verbose: 是否显示日志
        
    Returns:
        bool: 调整成功返回True，否则返回False
    """
    adjuster = WindowAdjuster(verbose=verbose)
    return adjuster.adjust_window(window_obj, x, y, width, height)


def quick_fullscreen_adjust(window_obj, verbose: bool = True) -> bool:
    """
    快速调整到全屏 1920x1080
    
    Args:
        window_obj: 窗口对象
        verbose: 是否显示日志
        
    Returns:
        bool: 调整成功返回True，否则返回False
    """
    adjuster = WindowAdjuster(verbose=verbose)
    return adjuster.quick_adjust(window_obj, "fullhd")


# # 使用示例
# if __name__ == "__main__":
#     """
#     使用示例：
    
#     # 方式1: 使用类
#     adjuster = WindowAdjuster(verbose=True)
#     success = adjuster.adjust_window(window_object, 0, 0, 1920, 1080)
    
#     # 方式2: 使用快速调整
#     success = adjuster.quick_adjust(window_object, "fullhd")
    
#     # 方式3: 使用便捷函数
#     success = adjust_window_simple(window_object, 0, 0, 1920, 1080)
    
#     # 方式4: 快速全屏
#     success = quick_fullscreen_adjust(window_object)
    
#     # 方式5: 分步调整（适合特殊情况）
#     success = adjuster.step_adjust(window_object, 0, 0, 1920, 1080)
#     """
#     pass
#     """
#     智能窗口调整器 - 最高成功率版本
#     """
#     # 方法1: 标准多方法尝试
#     if multi_method_position_window(main_window, x, y, w, h):
#         return True
    
#     print("\n🔧 标准方法失败，尝试特殊处理...")
    
#     # 方法2: 分步调整
#     try:
#         window_info = get_window_info(main_window)
#         if window_info:
#             hwnd = window_info['hwnd']
            
#             print("📝 尝试分步调整...")
#             # 第一步：只调整位置
#             win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, x, y, 0, 0,
#                                 win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW)
#             time.sleep(0.5)
            
#             # 第二步：只调整大小
#             win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, w, h,
#                                 win32con.SWP_NOMOVE | win32con.SWP_SHOWWINDOW)
#             time.sleep(0.5)
            
#             # 第三步：最终确认
#             win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, x, y, w, h,
#                                 win32con.SWP_SHOWWINDOW | win32con.SWP_FRAMECHANGED)
            
#             time.sleep(1)
            
#             # 验证
#             final_info = get_window_info(main_window)
#             if final_info:
#                 current_x, current_y = final_info['position']
#                 current_w, current_h = final_info['size']
                
#                 pos_ok = abs(current_x - x) <= 20
#                 size_ok = abs(current_w - w) <= 50
                
#                 if pos_ok and size_ok:
#                     print("🎉 分步调整成功！")
#                     return True
#     except Exception as e:
#         print(f"分步调整失败：{e}")
    
#     return False



def launch_tonghuashun_process(ths_path=r"C:\同花顺软件\同花顺金融大师\hexin.exe"):
    """
    启动同花顺软件进程

    Args:
        ths_path (str): 同花顺软件路径

    Returns:
        subprocess.Popen: 进程对象，如果失败返回None
    """
    # 检查软件是否存在
    if not os.path.exists(ths_path):
        print(f"错误：找不到同花顺软件，路径：{ths_path}")
        return None

    try:
        print("正在启动同花顺软件...")
        process = subprocess.Popen(ths_path)
        print(f"同花顺进程已启动，PID: {process.pid}")
        return process
    except Exception as e:
        print(f"启动同花顺软件失败：{str(e)}")
        return None

def connect_to_window(process, max_retries=15, wait_time=2):
    """
    连接到同花顺窗口

    Args:
        process: 进程对象
        max_retries (int): 最大重试次数
        wait_time (int): 每次重试间隔时间

    Returns:
        pywinauto窗口对象，如果失败返回None
    """
    print("等待同花顺软件完全启动...")
    time.sleep(8)  # 等待软件启动

    app = None
    main_window = None
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 方法1：通过进程ID连接
            app = Application().connect(process=process.pid)
            windows = app.windows()

            # 查找主窗口（通常是可见的最大窗口）
            for window in windows:
                if window.is_visible() and window.rectangle().width() > 100:
                    main_window = window
                    print(f"通过进程ID找到窗口：{window.window_text()}")
                    break

            if main_window:
                break

        except Exception as e:
            print(f"进程ID连接尝试 {retry_count + 1}: {str(e)}")

        # 方法2：通过窗口标题连接
        try:
            possible_titles = ["同花顺", "hexin", "通达信", "金融大师", "THS"]
            for title in possible_titles:
                try:
                    app = Application().connect(title_re=f".*{title}.*")
                    windows = app.windows()
                    if windows:
                        for window in windows:
                            if window.is_visible():
                                main_window = window
                                print(f"通过标题找到窗口：{window.window_text()}")
                                break
                        if main_window:
                            break
                except:
                    continue

            if main_window:
                break

        except Exception as e:
            print(f"标题连接尝试 {retry_count + 1}: {str(e)}")

        time.sleep(wait_time)
        retry_count += 1
        print(f"等待窗口加载... ({retry_count}/{max_retries})")

    if main_window is None:
        print("错误：无法获取同花顺主窗口")
        print("请确保同花顺软件已正常启动")
        return None

    print(f"成功获取窗口句柄：{main_window.handle}")
    return main_window

def click_search_box(main_window, search_text="", press_enter=True):
    """
    点击搜索框并输入内容，可选择是否敲回车键确认

    Args:
        main_window: pywinauto窗口对象
        search_text (str): 要输入的搜索内容
        press_enter (bool): 是否在输入完成后敲回车键，默认为True

    Returns:
        bool: 操作是否成功
    """
    try:
        print("正在查找搜索框...")

        # # 方法1：通过Value属性定位
        # try:
        #     search_box = main_window.child_window(
        #         control_type="Edit",
        #         title="代码/名称/简拼/功能"
        #     )
        #     print("通过Value属性找到搜索框")
        #     search_box.click()

        #     if search_text:
        #         time.sleep(0.5)  # 等待焦点
        #         # 清除现有内容并输入新内容
        #         search_box.select_all()
        #         search_box.type_keys(search_text)
        #         print(f"已输入搜索内容：{search_text}")

        #         # 敲回车键确认
        #         if press_enter:
        #             time.sleep(0.3)  # 短暂等待输入完成
        #             search_box.type_keys("{ENTER}")
        #             print("已敲回车键确认")

        #     return True

        # except Exception as e1:
        #     print(f"方法1失败：{e1}")

        # # 方法2：遍历查找编辑框
        # try:
        #     print("尝试遍历查找编辑框...")
        #     edit_controls = main_window.descendants(control_type="Edit")

        #     for i, ctrl in enumerate(edit_controls):
        #         try:
        #             # 检查控件文本
        #             ctrl_text = ctrl.window_text()
        #             print(f"找到编辑框 {i+1}: '{ctrl_text}'")

        #             if "代码/名称/简拼/功能" in ctrl_text or ctrl_text == "":
        #                 print(f"选择编辑框 {i+1} 进行点击")
        #                 ctrl.click()

        #                 if search_text:
        #                     time.sleep(0.5)
        #                     ctrl.select_all()
        #                     ctrl.type_keys(search_text)
        #                     print(f"已输入搜索内容：{search_text}")

        #                     # 敲回车键确认
        #                     if press_enter:
        #                         time.sleep(0.3)
        #                         ctrl.type_keys("{ENTER}")
        #                         print("已敲回车键确认")

        #                 return True

        #         except Exception as e2:
        #             print(f"处理编辑框 {i+1} 时出错：{e2}")
        #             continue

        # except Exception as e3:
        #     print(f"方法2失败：{e3}")
            
        try:
            template_path = r"./resources/images/search_box_template.png"
            location = pyautogui.locateOnScreen(
                template_path,
                confidence=0.8
            )

            if location:
                # 计算中心点
                x = location.left + location.width // 2
                y = location.top + location.height // 2
                
                pywinauto.mouse.click(coords=(x, y))
                print(f"已点击坐标 ({x}, {y})")

                if search_text:
                    time.sleep(0.5)
                    # 使用键盘输入
                    pywinauto.keyboard.send_keys("^a")  # Ctrl+A 全选
                    time.sleep(0.2)
                    pywinauto.keyboard.send_keys(search_text)
                    print(f"已输入搜索内容：{search_text}")

                    # 敲回车键确认
                    if press_enter:
                        time.sleep(0.3)
                        pywinauto.keyboard.send_keys("{ENTER}")
                        print("已敲回车键确认")
            return True
        except:
            print(f"方法3使用pyautogui自带截图失败：{e3}")
            return False            
        # try:
        #     # 方法3：使用坐标点击（备用方案）
        #     print("尝试使用坐标点击...")
        #     x = (1617 + 1807) // 2  # 1712
        #     y = (1058 + 1074) // 2  # 1066

        #     pywinauto.mouse.click(coords=(x, y))
        #     print(f"已点击坐标 ({x}, {y})")

        #     if search_text:
        #         time.sleep(0.5)
        #         # 使用键盘输入
        #         pywinauto.keyboard.send_keys("^a")  # Ctrl+A 全选
        #         time.sleep(0.2)
        #         pywinauto.keyboard.send_keys(search_text)
        #         print(f"已输入搜索内容：{search_text}")

        #         # 敲回车键确认
        #         if press_enter:
        #             time.sleep(0.3)
        #             pywinauto.keyboard.send_keys("{ENTER}")
        #             print("已敲回车键确认")
        # except:
        #     print(f"处理编辑框 {i+1} 时出错：{e2}")
        # return True

    except Exception as e:
        print(f"点击搜索框失败：{str(e)}")
        return False

def click_button_input_by_coor(search_text="", click_coor = [1712, 1066], press_enter=True):
        """
        方法1：使用坐标点击（备用方案）
        click_coor: [1712, 1066]
        search_text="" 需要输入的搜索内容
        press_enter=True 是否在输入完成后敲回车键，默认为True
        """

        print("尝试使用坐标点击...")
        x = click_coor[0]
        y = click_coor[1]

        pywinauto.mouse.click(coords=(x, y))
        print(f"已点击坐标 ({x}, {y})")

        if search_text:
            time.sleep(0.5)
            # 使用键盘输入
            pywinauto.keyboard.send_keys("^a")  # Ctrl+A 全选
            time.sleep(0.2)
            pywinauto.keyboard.send_keys(search_text)
            print(f"已输入搜索内容：{search_text}")

            # 敲回车键确认
            if press_enter:
                time.sleep(0.3)
                pywinauto.keyboard.send_keys("{ENTER}")
                print("已敲回车键确认")

def search_stock(main_window, stock_code, wait_after_enter=2):
    """
    搜索股票的便捷函数

    Args:
        main_window: pywinauto窗口对象
        stock_code (str): 股票代码或名称
        wait_after_enter (int): 敲回车后等待时间（秒）

    Returns:
        bool: 操作是否成功
    """
    print(f"正在搜索股票：{stock_code}")

    success = click_search_box(main_window, stock_code, press_enter=True)

    if success and wait_after_enter > 0:
        print(f"等待 {wait_after_enter} 秒加载搜索结果...")
        time.sleep(wait_after_enter)

    return success

def check_screenshot_dependencies():
    """
    检查截图功能的依赖是否可用

    Returns:
        bool: 依赖是否可用
    """
    if not SCREENSHOT_AVAILABLE:
        print("错误: 截图功能不可用，请安装依赖包:")
        print("pip install opencv-python numpy Pillow pywin32")
        return False
    return True

def capture_window_screenshot(window, save_path=None):
    """
    对指定窗口进行截图

    Args:
        window: pywinauto窗口对象
        save_path (str): 保存截图的路径，如果为None则不保存

    Returns:
        numpy.ndarray: 截图的numpy数组，如果失败返回None
    """
    if not check_screenshot_dependencies():
        return None

    try:
        # 获取窗口句柄
        hwnd = window.handle

        # 将窗口置于前台
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.5)  # 等待窗口切换

        # 获取窗口位置和大小
        rect = win32gui.GetWindowRect(hwnd)
        left, top, right, bottom = rect
        width = right - left
        height = bottom - top

        print(f"窗口位置: ({left}, {top}), 大小: {width}x{height}")

        # 截图
        screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))

        # 转换为numpy数组
        screenshot_np = np.array(screenshot)

        # 转换颜色格式 (PIL使用RGB，OpenCV使用BGR)
        screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

        # 保存截图
        if save_path:
            cv2.imwrite(save_path, screenshot_bgr)
            print(f"截图已保存到: {save_path}")

        return screenshot_bgr

    except Exception as e:
        print(f"截图失败: {str(e)}")
        return None

def find_template_in_screenshot(screenshot, template_path, threshold=0.8):
    """
    在截图中查找模板图像

    Args:
        screenshot: 截图的numpy数组
        template_path (str): 模板图像文件路径
        threshold (float): 匹配阈值，默认0.8

    Returns:
        dict: 包含匹配结果的字典，格式为:
              {
                  'found': bool,  # 是否找到
                  'confidence': float,  # 最高匹配度
                  'location': tuple,  # 匹配位置 (x, y)
                  'center': tuple,  # 匹配中心点 (x, y)
                  'rectangle': tuple  # 匹配矩形 (x, y, w, h)
              }
    """
    if not check_screenshot_dependencies():
        return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

    try:
        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            print(f"模板文件不存在: {template_path}")
            return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

        # 读取模板图像
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            print(f"无法读取模板图像: {template_path}")
            return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

        # 获取模板尺寸
        template_height, template_width = template.shape[:2]

        # 模板匹配
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

        # 获取最佳匹配位置
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        # 判断是否找到匹配
        found = max_val >= threshold

        if found:
            # 计算匹配位置和中心点
            top_left = max_loc
            center_x = top_left[0] + template_width // 2
            center_y = top_left[1] + template_height // 2

            result_dict = {
                'found': True,
                'confidence': max_val,
                'location': top_left,
                'center': (center_x, center_y),
                'rectangle': (top_left[0], top_left[1], template_width, template_height)
            }

            print(f"找到模板匹配! 置信度: {max_val:.3f}, 位置: {top_left}, 中心: ({center_x}, {center_y})")
        else:
            result_dict = {
                'found': False,
                'confidence': max_val,
                'location': None,
                'center': None,
                'rectangle': None
            }
            print(f"未找到模板匹配. 最高置信度: {max_val:.3f} (阈值: {threshold})")

        return result_dict

    except Exception as e:
        print(f"模板匹配失败: {str(e)}")
        return {'found': False, 'confidence': 0, 'location': None, 'center': None, 'rectangle': None}

def click_template_if_found(window, template_path, threshold=0.8, click_offset=(0, 0)):
    """
    在窗口中查找模板并点击

    Args:
        window: pywinauto窗口对象
        template_path (str): 模板图像文件路径
        threshold (float): 匹配阈值
        click_offset (tuple): 点击偏移量 (x_offset, y_offset)

    Returns:
        bool: 是否成功找到并点击
    """
    try:
        # 截图
        screenshot = capture_window_screenshot(window)
        if screenshot is None:
            return False

        # 查找模板
        match_result = find_template_in_screenshot(screenshot, template_path, threshold)

        if match_result['found']:
            # 获取窗口位置
            rect = win32gui.GetWindowRect(window.handle)
            window_left, window_top = rect[0], rect[1]

            # 计算点击坐标（相对于屏幕）
            center_x, center_y = match_result['center']
            click_x = window_left + center_x + click_offset[0]
            click_y = window_top + center_y + click_offset[1]

            # 点击
            pywinauto.mouse.click(coords=(click_x, click_y))
            print(f"已点击模板位置: ({click_x}, {click_y})")

            return True
        else:
            print("未找到模板，无法点击")
            return False

    except Exception as e:
        print(f"模板点击失败: {str(e)}")
        return False

def activate_window_to_front(window, force_activate=True):
    """
    将窗口激活并置于最前端

    Args:
        window: pywinauto窗口对象
        force_activate (bool): 是否强制激活窗口（处理一些顽固的窗口）

    Returns:
        bool: 操作是否成功
    """
    try:
        print("正在激活窗口到最前端...")

        # 方法1：使用pywinauto的标准方法
        try:
            # 确保窗口可见
            if not window.is_visible():
                window.restore()
                time.sleep(0.2)

            # 将窗口置于前台
            window.set_focus()
            time.sleep(0.1)

            # 激活窗口
            window.activate()
            time.sleep(0.2)

            print("使用pywinauto标准方法激活窗口成功")
            return True

        except Exception as e1:
            print(f"pywinauto标准方法失败：{e1}")

            if not force_activate:
                return False

        # 方法2：使用Windows API强制激活
        try:
            hwnd = window.handle

            # 检查窗口是否最小化，如果是则恢复
            if win32gui.IsIconic(hwnd):
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.2)

            # 将窗口置于最前端
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)

            # 激活窗口
            win32gui.SetActiveWindow(hwnd)
            time.sleep(0.1)

            # 确保窗口在最顶层
            win32gui.BringWindowToTop(hwnd)
            time.sleep(0.1)

            print("使用Windows API强制激活成功")
            return True

        except Exception as e2:
            print(f"Windows API方法失败：{e2}")

        # 方法3：使用ShowWindow API
        try:
            hwnd = window.handle

            # 显示并激活窗口
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            time.sleep(0.1)
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            time.sleep(0.1)
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)

            print("使用ShowWindow API激活成功")
            return True

        except Exception as e3:
            print(f"ShowWindow API方法失败：{e3}")

        return False

    except Exception as e:
        print(f"激活窗口失败：{str(e)}")
        return False

def position_window(main_window, x=0, y=0, w=1920, h=1080):
    """
    将窗口移动到 (x,y) 并调整为 w×h。
    尝试使用 pywinauto 的 move_window，失败则回退到 Win32 API。
    """
    try:
        try:
            wobj = main_window.wrapper_object()
        except Exception:
            wobj = main_window

        # 若最小化则恢复
        try:
            if hasattr(wobj, "is_minimized") and wobj.is_minimized():
                wobj.restore()
                time.sleep(0.2)
        except Exception:
            pass

        # # 先用 pywinauto 尝试
        # try:
        #     wobj.move_window(x, y, w, h, repaint=True)
        #     print(f"窗口已通过 pywinauto 移动到({x},{y}) 且调整为 {w}x{h}")
        #     return True
        # except Exception as e1:
        #     print(f"pywinauto move_window 失败，尝试 Win32 API：{e1}")

        # 回退到 Win32 API
        try:
            hwnd = wobj.handle
            SWP_SHOWWINDOW = 0x0040
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, x, y, w, h, SWP_SHOWWINDOW)
            print(f"窗口已通过 Win32 API 移动到({x},{y}) 且调整为 {w}x{h}")
            return True
        except Exception as e2:
            print(f"Win32 SetWindowPos 失败：{e2}")
            return False

    except Exception as e:
        print(f"设置窗口位置/尺寸失败: {e}")
        return False

def ensure_window_position_and_size(main_window, target_x=0, target_y=0, target_w=1920, target_h=1080, max_retries=8, tolerance=2, settle_sleep=0.15):
    """
    反复移动/调整并通过 Win32 API 校验，直到窗口位于指定坐标与尺寸（允许微小偏差）。
    """
    try:
        try:
            wobj = main_window.wrapper_object()
        except Exception:
            wobj = main_window

        hwnd = getattr(wobj, 'handle', None) or wobj.handle

        for attempt in range(1, max_retries + 1):
            # 调整
            ok = position_window(wobj, target_x, target_y, target_w, target_h)
            if not ok:
                print(f"第 {attempt}/{max_retries} 次调整失败，继续重试...")
            time.sleep(settle_sleep)

            # 校验
            try:
                left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                cur_w = right - left
                cur_h = bottom - top
            except Exception as e:
                print(f"读取窗口矩形失败: {e}")
                continue

            dx = abs(left - target_x)
            dy = abs(top - target_y)
            dw = abs(cur_w - target_w)
            dh = abs(cur_h - target_h)

            print(f"调整后窗口: pos=({left},{top}), size={cur_w}x{cur_h}, 偏差 dx={dx}, dy={dy}, dw={dw}, dh={dh}")

            if dx <= tolerance and dy <= tolerance and dw <= tolerance and dh <= tolerance:
                print("✓ 窗口位置与尺寸已达到预期")
                return True

        print("✗ 在最大重试次数内未能将窗口调整到预期位置与尺寸")
        return False

    except Exception as e:
        print(f"ensure_window_position_and_size 失败: {e}")
        return False

def main():
    """
    主函数：启动同花顺并演示截图和模板匹配功能
    """
    # 启动进程
    process = launch_tonghuashun_process()
    if not process:
        return

    # 连接窗口
    main_window = connect_to_window(process)
    if not main_window:
        return

    # 将主窗口移动到 (0,0) 并调整为 1920x1080
    position_window(main_window, 0, 0, 1920, 1080)

    # 示例1：对窗口进行截图
    print("\n=== 窗口截图功能演示 ===")
    screenshot_path = "tonghuashun_screenshot.png"
    screenshot = capture_window_screenshot(main_window, screenshot_path)

    if screenshot is not None:
        print(f"截图成功！图像尺寸: {screenshot.shape}")

        # 示例2：模板匹配演示
        print("\n=== 模板匹配功能演示 ===")
        # 注意：你需要准备一个模板图像文件
        template_path = "search_box_template.png"  # 搜索框的模板图像

        if os.path.exists(template_path):
            match_result = find_template_in_screenshot(screenshot, template_path, threshold=0.7)

            if match_result['found']:
                print(f"模板匹配成功！置信度: {match_result['confidence']:.3f}")
                print(f"匹配位置: {match_result['location']}")
                print(f"中心点: {match_result['center']}")

                # 示例3：点击找到的模板位置
                print("\n=== 模板点击功能演示 ===")
                click_success = click_template_if_found(main_window, template_path, threshold=0.7)
                if click_success:
                    print("模板点击成功！")
                    time.sleep(1)

                    # 在点击后输入内容
                    pywinauto.keyboard.send_keys("000001{ENTER}")
                    print("已输入股票代码并回车")
            else:
                print("未找到模板匹配")
        else:
            print(f"模板文件不存在: {template_path}")
            print("请准备一个搜索框的模板图像文件")

    # 原有的搜索功能演示
    print("\n=== 原有搜索功能演示 ===")
    success = search_stock(main_window, "000001")  # 搜索平安银行

    if success:
        print("搜索操作完成")

        # 等待一段时间后进行下一次搜索
        time.sleep(3)

        # 再次截图查看搜索结果
        result_screenshot_path = "search_result_screenshot.png"
        result_screenshot = capture_window_screenshot(main_window, result_screenshot_path)
        if result_screenshot is not None:
            print(f"搜索结果截图已保存: {result_screenshot_path}")

        # 示例：搜索股票名称
        search_stock(main_window, "招商银行")

    else:
        print("搜索操作失败")

    # 可选：打印窗口控件信息用于调试
    # print("\n=== 窗口控件信息 ===")
    # main_window.print_control_identifiers()

def demo_template_creation():
    """
    演示如何创建模板图像的辅助函数
    """
    print("=== 模板创建演示 ===")
    print("1. 首先运行程序对窗口截图")
    print("2. 使用图像编辑软件（如画图、Photoshop等）打开截图")
    print("3. 裁剪出你想要匹配的区域（如搜索框）")
    print("4. 保存为模板图像文件（如 search_box_template.png）")
    print("5. 将模板文件放在程序同目录下")
    print("6. 重新运行程序即可使用模板匹配功能")

def advanced_template_matching_demo(main_window):
    """
    高级模板匹配演示
    """
    print("\n=== 高级模板匹配演示 ===")

    # 多个模板匹配
    templates = [
        ("search_box_template.png", "搜索框"),
        ("buy_button_template.png", "买入按钮"),
        ("sell_button_template.png", "卖出按钮"),
        ("menu_template.png", "菜单按钮")
    ]

    # 截图
    screenshot = capture_window_screenshot(main_window)
    if screenshot is None:
        return

    found_templates = []

    for template_path, template_name in templates:
        if os.path.exists(template_path):
            match_result = find_template_in_screenshot(screenshot, template_path, threshold=0.7)
            if match_result['found']:
                found_templates.append((template_name, match_result))
                print(f"找到 {template_name}: 置信度 {match_result['confidence']:.3f}")
        else:
            print(f"模板文件不存在: {template_path}")

    if found_templates:
        print(f"\n总共找到 {len(found_templates)} 个模板匹配")

        # 可以根据需要点击特定的模板
        for template_name, match_result in found_templates:
            print(f"{template_name} 位置: {match_result['center']}")
    else:
        print("未找到任何模板匹配")

def check_pyautogui_dependencies():
    """
    检查pyautogui功能的依赖是否可用

    Returns:
        bool: 依赖是否可用
    """
    if not PYAUTOGUI_AVAILABLE:
        print("错误: pyautogui功能不可用，请安装依赖包:")
        print("pip install pyautogui")
        return False
    return True

def locate_template_on_screen(template_path, confidence=0.8, region=None):
    """
    使用pyautogui在屏幕上查找模板图像

    Args:
        template_path (str): 模板图像文件路径
        confidence (float): 匹配置信度，默认0.8
        region (tuple): 搜索区域 (left, top, width, height)，None表示全屏

    Returns:
        dict: 包含匹配结果的字典，格式为:
              {
                  'found': bool,  # 是否找到
                  'confidence': float,  # 匹配置信度
                  'location': tuple,  # 匹配位置 (left, top, width, height)
                  'center': tuple,  # 匹配中心点 (x, y)
              }
    """
    if not check_pyautogui_dependencies():
        return {'found': False, 'confidence': 0, 'location': None, 'center': None}

    try:
        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            print(f"模板文件不存在: {template_path}")
            return {'found': False, 'confidence': 0, 'location': None, 'center': None}

        print(f"在屏幕上查找模板: {template_path}")
        print(f"置信度阈值: {confidence}")
        if region:
            print(f"搜索区域: {region}")

        # 使用pyautogui查找图像
        try:
            location = pyautogui.locateOnScreen(
                template_path,
                confidence=confidence,
                region=region
            )

            if location:
                # 计算中心点
                center_x = location.left + location.width // 2
                center_y = location.top + location.height // 2

                result = {
                    'found': True,
                    'confidence': confidence,  # pyautogui不返回实际置信度
                    'location': (location.left, location.top, location.width, location.height),
                    'center': (center_x, center_y)
                }

                print(f"✓ 找到模板匹配!")
                print(f"  位置: ({location.left}, {location.top})")
                print(f"  大小: {location.width}x{location.height}")
                print(f"  中心: ({center_x}, {center_y})")

                return result
            else:
                print("✗ 未找到模板匹配")
                return {'found': False, 'confidence': 0, 'location': None, 'center': None}

        except pyautogui.ImageNotFoundException:
            print("✗ 未找到模板匹配 (ImageNotFoundException)")
            return {'found': False, 'confidence': 0, 'location': None, 'center': None}

    except Exception as e:
        print(f"pyautogui模板匹配失败: {str(e)}")
        return {'found': False, 'confidence': 0, 'location': None, 'center': None}

def click_template_on_screen(template_path, confidence=0.8, region=None, click_offset=(0, 0), button='left'):
    """
    使用pyautogui在屏幕上查找模板并点击

    Args:
        template_path (str): 模板图像文件路径
        confidence (float): 匹配置信度，默认0.8
        region (tuple): 搜索区域 (left, top, width, height)，None表示全屏
        click_offset (tuple): 点击偏移量 (x_offset, y_offset)
        button (str): 鼠标按钮 ('left', 'right', 'middle')

    Returns:
        bool: 是否成功找到并点击
    """
    if not check_pyautogui_dependencies():
        return False

    try:
        # 查找模板
        match_result = locate_template_on_screen(template_path, confidence, region)

        if match_result['found']:
            # 计算点击坐标
            center_x, center_y = match_result['center']
            click_x = center_x + click_offset[0]
            click_y = center_y + click_offset[1]

            # 点击
            pyautogui.click(click_x, click_y, button=button)
            print(f"✓ 已点击位置: ({click_x}, {click_y})")

            return True
        else:
            print("✗ 未找到模板，无法点击")
            return False

    except Exception as e:
        print(f"pyautogui模板点击失败: {str(e)}")
        return False

def wait_for_template_on_screen(template_path, timeout=10, confidence=0.8, region=None, check_interval=0.5):
    """
    等待模板图像出现在屏幕上

    Args:
        template_path (str): 模板图像文件路径
        timeout (float): 超时时间（秒）
        confidence (float): 匹配置信度，默认0.8
        region (tuple): 搜索区域 (left, top, width, height)，None表示全屏
        check_interval (float): 检查间隔时间（秒）

    Returns:
        dict: 匹配结果，如果超时则found为False
    """
    if not check_pyautogui_dependencies():
        return {'found': False, 'confidence': 0, 'location': None, 'center': None}

    print(f"等待模板出现: {template_path}")
    print(f"超时时间: {timeout}秒, 检查间隔: {check_interval}秒")

    start_time = time.time()

    while time.time() - start_time < timeout:
        result = locate_template_on_screen(template_path, confidence, region)

        if result['found']:
            elapsed_time = time.time() - start_time
            print(f"✓ 模板出现! 等待时间: {elapsed_time:.1f}秒")
            return result

        time.sleep(check_interval)

    print(f"✗ 等待超时 ({timeout}秒)")
    return {'found': False, 'confidence': 0, 'location': None, 'center': None}

def get_window_region(window):
    """
    获取窗口的屏幕区域坐标

    Args:
        window: pywinauto窗口对象

    Returns:
        tuple: (left, top, width, height) 或 None
    """
    try:
        if not SCREENSHOT_AVAILABLE:
            print("需要win32gui支持获取窗口区域")
            return None

        # 获取窗口位置
        rect = win32gui.GetWindowRect(window.handle)
        left, top, right, bottom = rect
        width = right - left
        height = bottom - top

        return (left, top, width, height)

    except Exception as e:
        print(f"获取窗口区域失败: {str(e)}")
        return None

def locate_template_in_window(window, template_path, confidence=0.8):
    """
    在指定窗口中查找模板（结合pyautogui和窗口区域）

    Args:
        window: pywinauto窗口对象
        template_path (str): 模板图像文件路径
        confidence (float): 匹配置信度，默认0.8

    Returns:
        dict: 匹配结果
    """
    # 获取窗口区域
    region = get_window_region(window)
    if region is None:
        print("无法获取窗口区域，使用全屏搜索")
        region = None
    else:
        print(f"在窗口区域搜索: {region}")

    # 在指定区域查找模板
    return locate_template_on_screen(template_path, confidence, region)

def click_template_in_window(window, template_path, confidence=0.8, click_offset=(0, 0), button='left'):
    """
    在指定窗口中查找模板并点击

    Args:
        window: pywinauto窗口对象
        template_path (str): 模板图像文件路径
        confidence (float): 匹配置信度，默认0.8
        click_offset (tuple): 点击偏移量 (x_offset, y_offset)
        button (str): 鼠标按钮 ('left', 'right', 'middle')

    Returns:
        bool: 是否成功找到并点击
    """
    # 获取窗口区域
    region = get_window_region(window)
    if region is None:
        print("无法获取窗口区域，使用全屏搜索")
        region = None

    # 在指定区域查找并点击模板
    return click_template_on_screen(template_path, confidence, region, click_offset, button)


def unit_test_adjust_window():
    process = launch_tonghuashun_process()
    ths_window = connect_to_window(process)
    # position_window(ths_window)
    # activate_window_to_front(ths_window)
    # success = smart_window_adjuster(ths_window, 0, 0, 1920, 1080)
    


if __name__ == "__main__":
    # template_path = r"C:\Users\<USER>\AppData\Local\ShadowBot\users\850615789736157186\apps\6f4d191f-e465-4c3a-950b-a3955030c4d9\xbot_robot\search_box_template.png"
    # click_template_on_screen(template_path, confidence=0.8, region=None, click_offset=(0, 0), button='left')
    # main()
    unit_test_adjust_window()
# 使用示例
# if __name__ == "__main__":
#     """
#     使用方法：
    
#     # 基本使用
#     success = smart_window_adjuster(main_window, 0, 0, 1920, 1080)
    
#     # 或者使用多方法版本获得更详细的控制
#     success = multi_method_position_window(main_window, 0, 0, 1920, 1080, max_attempts=10)
#     """
#     process = launch_tonghuashun_process()
#     ths_window = connect_to_window(process)
#     pass