# demo_integration.py - 演示如何在现有代码中集成新的日志系统
import time
import random

try:
    from logger_manager import (
        log_info,
        log_warning,
        log_error,
        log_success,
        log_progress,
        LogCaptureContext,
        get_logger
    )
    print("日志管理器导入成功")
except ImportError as e:
    print(f"导入日志管理器失败: {e}")
    # 提供备用的日志函数
    def log_info(msg): print(f"[INFO] {msg}")
    def log_warning(msg): print(f"[WARNING] {msg}")
    def log_error(msg): print(f"[ERROR] {msg}")
    def log_success(msg): print(f"[SUCCESS] {msg}")
    def log_progress(msg): print(f"[PROGRESS] {msg}")

    class LogCaptureContext:
        def __enter__(self): return self
        def __exit__(self, *args): pass

    def get_logger():
        class DummyLogger:
            def start_capture(self): pass
            def stop_capture(self): pass
            def get_logs(self): return []
        return DummyLogger()

def demo_old_style_logging():
    """演示旧式的print日志输出"""
    print("=== 旧式日志输出演示 ===")
    print("开始处理数据...")
    print("连接数据库...")
    print("查询股票列表...")
    print("处理完成")
    print()

def demo_new_style_logging():
    """演示新式的结构化日志输出"""
    log_info("=== 新式日志输出演示 ===")
    log_progress("开始处理数据...")
    log_info("连接数据库...")
    log_info("查询股票列表...")
    log_success("处理完成")
    print()

def demo_mixed_logging():
    """演示混合日志输出（在日志捕获环境中）"""
    log_info("=== 混合日志输出演示 ===")
    
    with LogCaptureContext():
        log_progress("启动日志捕获...")
        print("这是一条普通的print语句")
        log_info("这是结构化日志")
        print("处理中...")
        log_warning("发现潜在问题")
        print("继续处理...")
        log_success("处理完成")
    
    log_info("日志捕获结束")
    print()

def demo_stock_processing_simulation():
    """模拟股票处理过程的日志输出"""
    log_info("=== 股票处理模拟演示 ===")
    
    # 模拟股票列表
    stocks = [
        ("000001", "平安银行"),
        ("000002", "万科A"),
        ("000858", "五粮液"),
        ("600036", "招商银行"),
        ("600519", "贵州茅台")
    ]
    
    log_info(f"开始处理 {len(stocks)} 只股票")
    
    for i, (code, name) in enumerate(stocks):
        log_progress(f"处理股票 {i+1}/{len(stocks)}: {code} {name}")
        
        # 模拟处理时间
        time.sleep(0.5)
        
        # 模拟不同的处理结果
        result = random.choice(["success", "warning", "error"])
        
        if result == "success":
            log_success(f"股票 {code} {name} 处理成功")
        elif result == "warning":
            log_warning(f"股票 {code} {name} 处理时发现警告")
        else:
            log_error(f"股票 {code} {name} 处理失败")
    
    log_success("所有股票处理完成")
    print()

def demo_error_handling():
    """演示错误处理的日志输出"""
    log_info("=== 错误处理演示 ===")
    
    try:
        log_progress("尝试连接数据库...")
        # 模拟数据库连接错误
        if random.choice([True, False]):
            raise ConnectionError("数据库连接失败")
        log_success("数据库连接成功")
        
        log_progress("执行查询...")
        # 模拟查询错误
        if random.choice([True, False]):
            raise ValueError("查询参数错误")
        log_success("查询执行成功")
        
    except ConnectionError as e:
        log_error(f"连接错误: {e}")
        log_warning("尝试重新连接...")
        time.sleep(1)
        log_success("重新连接成功")
        
    except ValueError as e:
        log_error(f"参数错误: {e}")
        log_info("使用默认参数重试...")
        log_success("重试成功")
        
    except Exception as e:
        log_error(f"未知错误: {e}")
    
    print()

def demo_progress_tracking():
    """演示进度跟踪的日志输出"""
    log_info("=== 进度跟踪演示 ===")
    
    total_steps = 10
    log_info(f"开始执行任务，共 {total_steps} 步")
    
    for step in range(1, total_steps + 1):
        log_progress(f"执行步骤 {step}/{total_steps} ({step/total_steps*100:.1f}%)")
        
        # 模拟处理时间
        time.sleep(0.3)
        
        # 在某些步骤添加额外信息
        if step == 3:
            log_info("正在加载配置文件...")
        elif step == 5:
            log_warning("检测到配置项缺失，使用默认值")
        elif step == 8:
            log_info("正在保存结果...")
    
    log_success("任务执行完成")
    print()

def demo_with_print_capture():
    """演示在print捕获环境中的日志输出"""
    log_info("=== Print捕获环境演示 ===")
    
    logger = get_logger()
    logger.start_capture()
    
    try:
        print("这条print语句会被捕获")
        log_info("这是结构化日志")
        print("处理数据中...")
        log_progress("进度: 50%")
        print("数据处理完成")
        log_success("操作成功")
        
        # 获取捕获的日志
        logs = logger.get_logs()
        log_info(f"捕获了 {len(logs)} 条日志")
        
    finally:
        logger.stop_capture()
    
    print()

def main():
    """主演示函数"""
    print("日志系统集成演示")
    print("=" * 50)
    
    # 演示不同的日志输出方式
    demo_old_style_logging()
    demo_new_style_logging()
    demo_mixed_logging()
    demo_stock_processing_simulation()
    demo_error_handling()
    demo_progress_tracking()
    demo_with_print_capture()
    
    log_success("所有演示完成")
    print("\n" + "=" * 50)
    print("演示结束")
    print("\n提示：")
    print("1. 启动 'python app.py' 来查看Web控制台")
    print("2. 在Web界面中点击'开始任务'来运行实际任务")
    print("3. 所有日志都会实时显示在Web终端中")

if __name__ == "__main__":
    main()
