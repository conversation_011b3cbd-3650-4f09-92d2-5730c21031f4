# test_logger.py - 测试日志系统
import time
from logger_manager import (
    get_logger, 
    log_info, 
    log_warning, 
    log_error, 
    log_success, 
    log_progress,
    LogCaptureContext
)

def test_basic_logging():
    """测试基本日志功能"""
    print("=== 测试基本日志功能 ===")
    
    log_info("这是一条信息日志")
    log_warning("这是一条警告日志")
    log_error("这是一条错误日志")
    log_success("这是一条成功日志")
    log_progress("这是一条进度日志")
    
    print("基本日志测试完成")

def test_print_capture():
    """测试print语句捕获"""
    print("\n=== 测试print语句捕获 ===")
    
    with LogCaptureContext():
        print("这条print语句应该被捕获到日志系统")
        print("这是第二条print语句")
        print("包含数字的消息: 123")
        print("包含特殊字符的消息: !@#$%^&*()")
    
    print("print捕获测试完成")

def test_mixed_logging():
    """测试混合日志输出"""
    print("\n=== 测试混合日志输出 ===")
    
    logger = get_logger()
    logger.start_capture()
    
    try:
        log_info("开始混合日志测试")
        print("这是一条普通的print语句")
        log_progress("处理进度: 25%")
        print("处理中...")
        log_progress("处理进度: 50%")
        print("继续处理...")
        log_progress("处理进度: 75%")
        print("即将完成...")
        log_progress("处理进度: 100%")
        log_success("混合日志测试完成")
        
        # 获取并显示日志
        logs = logger.get_logs()
        print(f"\n捕获到 {len(logs)} 条日志:")
        for log in logs:
            print(f"  {log}")
            
    finally:
        logger.stop_capture()

def test_simulated_task():
    """模拟一个任务的日志输出"""
    print("\n=== 模拟任务日志输出 ===")
    
    with LogCaptureContext() as logger:
        log_info("开始执行模拟任务")
        
        # 模拟初始化
        log_progress("正在初始化...")
        time.sleep(0.5)
        print("初始化数据库连接")
        print("加载配置文件")
        log_success("初始化完成")
        
        # 模拟处理过程
        total_items = 5
        for i in range(total_items):
            log_progress(f"处理项目 {i+1}/{total_items}")
            print(f"正在处理项目: item_{i+1}")
            
            if i == 2:
                log_warning("项目3处理时遇到警告")
                print("警告: 数据格式不标准，使用默认值")
            
            time.sleep(0.3)
            log_success(f"项目 {i+1} 处理完成")
        
        log_info("所有项目处理完成")
        log_success("模拟任务执行成功")
        
        # 显示捕获的日志统计
        logs = logger.get_logs()
        info_count = sum(1 for log in logs if '[INFO]' in log)
        success_count = sum(1 for log in logs if '[SUCCESS]' in log)
        warning_count = sum(1 for log in logs if '[WARNING]' in log)
        progress_count = sum(1 for log in logs if '[PROGRESS]' in log)
        
        print(f"\n日志统计:")
        print(f"  信息日志: {info_count}")
        print(f"  成功日志: {success_count}")
        print(f"  警告日志: {warning_count}")
        print(f"  进度日志: {progress_count}")
        print(f"  总计: {len(logs)}")

def main():
    """主测试函数"""
    print("开始测试日志管理系统")
    print("=" * 50)
    
    # 运行各种测试
    test_basic_logging()
    test_print_capture()
    test_mixed_logging()
    test_simulated_task()
    
    print("\n" + "=" * 50)
    print("日志系统测试完成")

if __name__ == "__main__":
    main()
