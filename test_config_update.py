#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件更新测试脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_update():
    """测试配置文件更新功能"""
    print("=" * 60)
    print("配置文件更新测试")
    print("=" * 60)
    
    print("本测试将验证交互式截图功能是否正确更新所有配置项")
    print()
    
    # 显示当前配置
    print("当前配置文件状态：")
    show_current_config()
    
    print("\n" + "=" * 60)
    input("按回车键开始交互式截图测试...")
    
    try:
        # 导入功能模块
        from windows_precessing import interactive_screenshot_capture
        
        print("正在启动交互式截图功能...")
        print("请完成以下操作：")
        print("1. 选择包含K线的屏幕区域")
        print("2. 选择K线中心点")
        print("3. 观察配置文件更新情况")
        print()
        
        # 执行交互式截图
        result = interactive_screenshot_capture()
        
        if result:
            print(f"\n✓ 交互式截图完成！")
            print(f"✓ 截图已保存到: {result}")
            
            print("\n" + "=" * 60)
            print("配置文件更新后的状态：")
            show_current_config()
            
            # 验证关键配置项
            verify_config_update()
            
        else:
            print("\n测试被取消或失败")
            
    except ImportError as e:
        print(f"导入模块失败: {e}")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

def show_current_config():
    """显示当前配置文件状态"""
    try:
        config_path = "resources/coor_config.json"
        
        if not os.path.exists(config_path):
            print("❌ 配置文件不存在")
            return
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 显示主要K线捕获坐标
        if "main_kline_capture_coor" in config:
            main_coor = config["main_kline_capture_coor"]
            print(f"📍 main_kline_capture_coor:")
            print(f"   p1: {main_coor.get('p1', 'N/A')}")
            print(f"   p2: {main_coor.get('p2', 'N/A')}")
        else:
            print("❌ main_kline_capture_coor 不存在")
        
        # 显示K线相关配置
        if "line_x" in config:
            line_x = config["line_x"]
            print(f"📊 line_x: {line_x} (数量: {len(line_x)})")
        else:
            print("❌ line_x 不存在")
        
        if "line_width" in config:
            print(f"📏 line_width: {config['line_width']}")
        else:
            print("❌ line_width 不存在")
        
        if "lines_range" in config:
            lines_range = config["lines_range"]
            print(f"📐 lines_range: 数量 {len(lines_range)}")
            if lines_range:
                print(f"   第一个范围: {lines_range[0]}")
                if len(lines_range) > 1:
                    print(f"   最后一个范围: {lines_range[-1]}")
        else:
            print("❌ lines_range 不存在")
        
        # 显示交互式截图信息
        if "interactive_capture" in config:
            interactive = config["interactive_capture"]
            print(f"🎯 interactive_capture:")
            print(f"   timestamp: {interactive.get('timestamp', 'N/A')}")
            print(f"   kline_count: {interactive.get('kline_count', 'N/A')}")
            if "kline_centers" in interactive:
                centers = interactive["kline_centers"]
                print(f"   kline_centers: {centers} (数量: {len(centers)})")
        else:
            print("❌ interactive_capture 不存在")
            
    except Exception as e:
        print(f"❌ 读取配置文件时发生错误: {e}")

def verify_config_update():
    """验证配置更新是否正确"""
    try:
        config_path = "resources/coor_config.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n" + "=" * 60)
        print("配置验证结果：")
        
        # 验证必要的配置项
        checks = [
            ("main_kline_capture_coor", "main_kline_capture_coor" in config),
            ("main_kline_capture_coor.p1", config.get("main_kline_capture_coor", {}).get("p1") is not None),
            ("main_kline_capture_coor.p2", config.get("main_kline_capture_coor", {}).get("p2") is not None),
            ("line_x", "line_x" in config and len(config.get("line_x", [])) > 0),
            ("line_width", "line_width" in config),
            ("lines_range", "lines_range" in config and len(config.get("lines_range", [])) > 0),
            ("interactive_capture", "interactive_capture" in config),
            ("interactive_capture.kline_centers", config.get("interactive_capture", {}).get("kline_centers") is not None),
            ("interactive_capture.timestamp", config.get("interactive_capture", {}).get("timestamp") is not None),
        ]
        
        all_passed = True
        for item, passed in checks:
            status = "✓" if passed else "❌"
            print(f"{status} {item}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 所有配置项都已正确更新！")
        else:
            print("⚠️  部分配置项更新失败，请检查代码")
        
        # 验证数据一致性
        verify_data_consistency(config)
        
    except Exception as e:
        print(f"❌ 验证配置时发生错误: {e}")

def verify_data_consistency(config):
    """验证数据一致性"""
    print("\n数据一致性检查：")
    
    try:
        # 检查K线数量一致性
        line_x = config.get("line_x", [])
        lines_range = config.get("lines_range", [])
        kline_centers = config.get("interactive_capture", {}).get("kline_centers", [])
        kline_count = config.get("interactive_capture", {}).get("kline_count", 0)
        
        if len(line_x) == len(lines_range) == len(kline_centers) == kline_count:
            print(f"✓ K线数量一致: {len(line_x)} 个")
        else:
            print(f"❌ K线数量不一致:")
            print(f"   line_x: {len(line_x)}")
            print(f"   lines_range: {len(lines_range)}")
            print(f"   kline_centers: {len(kline_centers)}")
            print(f"   kline_count: {kline_count}")
        
        # 检查K线坐标是否相同
        if line_x == kline_centers:
            print("✓ K线坐标数据一致")
        else:
            print("❌ K线坐标数据不一致:")
            print(f"   line_x: {line_x}")
            print(f"   kline_centers: {kline_centers}")
        
        # 检查K线范围计算是否正确
        line_width = config.get("line_width", 0)
        if lines_range and line_x:
            expected_range = [line_x[0] - line_width, line_x[0] + line_width]
            actual_range = lines_range[0]
            if expected_range == actual_range:
                print(f"✓ K线范围计算正确 (宽度: {line_width})")
            else:
                print(f"❌ K线范围计算错误:")
                print(f"   期望: {expected_range}")
                print(f"   实际: {actual_range}")
        
    except Exception as e:
        print(f"❌ 数据一致性检查时发生错误: {e}")

def main():
    """主函数"""
    print("配置文件更新测试工具")
    print("=" * 40)
    
    while True:
        print("\n选择测试模式：")
        print("1. 执行完整的配置更新测试")
        print("2. 查看当前配置文件状态")
        print("3. 验证配置文件完整性")
        print("4. 退出")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                test_config_update()
            elif choice == '2':
                show_current_config()
            elif choice == '3':
                verify_config_update()
            elif choice == '4':
                print("退出测试")
                break
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
