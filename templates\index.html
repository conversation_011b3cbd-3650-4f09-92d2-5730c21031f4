<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python程序控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .content {
            padding: 40px;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #007bff;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            align-items: center;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 600;
            color: #495057;
        }

        .status-value {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .status-running {
            background: #d4edda;
            color: #155724;
        }

        .status-paused {
            background: #fff3cd;
            color: #856404;
        }

        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }

        .status-idle {
            background: #e2e3e5;
            color: #495057;
        }

        .progress-container {
            width: 100%;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            height: 20px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-start {
            background: #28a745;
            color: white;
        }

        .btn-pause {
            background: #ffc107;
            color: #212529;
        }

        .btn-resume {
            background: #17a2b8;
            color: white;
        }

        .btn-stop {
            background: #dc3545;
            color: white;
        }

        .log-panel {
            background: #212529;
            color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
            border: 2px solid #495057;
            position: relative;
        }

        .log-header {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #495057;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-controls {
            display: flex;
            gap: 10px;
        }

        .log-btn {
            background: #495057;
            color: #f8f9fa;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.2s;
        }

        .log-btn:hover {
            background: #6c757d;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px 0;
            border-left: 3px solid #007bff;
            padding-left: 10px;
            animation: fadeIn 0.3s ease;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .log-entry.log-info {
            border-left-color: #007bff;
        }

        .log-entry.log-success {
            border-left-color: #28a745;
            color: #90ee90;
        }

        .log-entry.log-warning {
            border-left-color: #ffc107;
            color: #ffeb3b;
        }

        .log-entry.log-error {
            border-left-color: #dc3545;
            color: #ff6b6b;
        }

        .log-entry.log-progress {
            border-left-color: #17a2b8;
            color: #87ceeb;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .debug-panel {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .debug-config {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .config-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .config-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            user-select: none;
        }

        .config-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-secondary:disabled {
            background: #adb5bd;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Python程序控制台</h1>
            <p>实时控制Python代码的运行、暂停和停止</p>
        </div>
        
        <div class="content">
            <div id="message-container"></div>
            
            <div class="status-panel">
                <div class="status-item">
                    <span class="status-label">运行状态:</span>
                    <span id="status" class="status-value status-idle">待机</span>
                </div>
                <div class="status-item">
                    <span class="status-label">进度:</span>
                    <div class="progress-container">
                        <div id="progress-bar" class="progress-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="status-item">
                    <span class="status-label">进度百分比:</span>
                    <span id="progress-text">0%</span>
                </div>
            </div>
            
            <div class="controls">
                <button id="start-btn" class="btn btn-start">开始任务</button>
                <button id="pause-btn" class="btn btn-pause" disabled>暂停</button>
                <button id="resume-btn" class="btn btn-resume" disabled>恢复</button>
                <button id="stop-btn" class="btn btn-stop" disabled>停止</button>
            </div>

            <!-- 调试配置面板 -->
            <div class="debug-panel">
                <div class="panel-header">🔧 调试配置</div>
                <div class="debug-config">
                    <div class="config-row">
                        <label class="config-label">
                            <input type="checkbox" id="debug-mode-checkbox"> 启用调试模式
                        </label>
                    </div>
                    <div class="config-row">
                        <label class="config-label">
                            <input type="checkbox" id="save-intermediate-checkbox"> 保存中间图像
                        </label>
                    </div>
                    <div class="config-row">
                        <label class="config-label">
                            <input type="checkbox" id="save-original-checkbox"> 保存原始截图
                        </label>
                    </div>
                    <div class="config-row">
                        <label class="config-label">
                            <input type="checkbox" id="save-cropped-checkbox"> 保存裁剪图像
                        </label>
                    </div>
                    <div class="config-row">
                        <label class="config-label">
                            <input type="checkbox" id="save-detection-checkbox"> 保存检测结果
                        </label>
                    </div>
                    <div class="config-row">
                        <button id="apply-debug-config-btn" class="btn btn-secondary">应用配置</button>
                        <button id="cleanup-debug-btn" class="btn btn-secondary">清理调试文件</button>
                    </div>
                </div>
            </div>
            
            <div class="log-panel">
                <div class="log-header">
                    <span>📋 运行日志</span>
                    <div class="log-controls">
                        <button id="clear-logs-btn" class="log-btn">清空日志</button>
                        <button id="auto-scroll-btn" class="log-btn">自动滚动: 开</button>
                        <button id="export-logs-btn" class="log-btn">导出日志</button>
                    </div>
                </div>
                <div id="log-container"></div>
            </div>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const resumeBtn = document.getElementById('resume-btn');
        const stopBtn = document.getElementById('stop-btn');
        const statusElement = document.getElementById('status');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const logContainer = document.getElementById('log-container');
        const messageContainer = document.getElementById('message-container');
        const clearLogsBtn = document.getElementById('clear-logs-btn');
        const autoScrollBtn = document.getElementById('auto-scroll-btn');
        const exportLogsBtn = document.getElementById('export-logs-btn');

        // 调试配置元素
        const debugModeCheckbox = document.getElementById('debug-mode-checkbox');
        const saveIntermediateCheckbox = document.getElementById('save-intermediate-checkbox');
        const saveOriginalCheckbox = document.getElementById('save-original-checkbox');
        const saveCroppedCheckbox = document.getElementById('save-cropped-checkbox');
        const saveDetectionCheckbox = document.getElementById('save-detection-checkbox');
        const applyDebugConfigBtn = document.getElementById('apply-debug-config-btn');
        const cleanupDebugBtn = document.getElementById('cleanup-debug-btn');

        // 状态轮询间隔
        let statusInterval;
        let autoScroll = true;
        let allLogs = [];

        // 显示消息
        function showMessage(message, type = 'success') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.textContent = message;
            messageContainer.appendChild(messageDiv);
            
            // 3秒后自动移除消息
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 发送API请求
        async function sendRequest(url, method = 'POST') {
            try {
                const response = await fetch(url, { method });
                const data = await response.json();

                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }

                // 成功后，立刻刷新一次状态
                getStatus();

                return data;
            } catch (error) {
                showMessage('网络请求失败', 'error');
                console.error('Request failed:', error);
                return { success: false };
            }
        }

        // 更新UI状态
        function updateUI(data) {
            // 更新状态显示
            statusElement.textContent = data.status;
            
            // 更新状态样式
            statusElement.className = 'status-value';
            if (data.is_running && !data.is_paused) {
                statusElement.classList.add('status-running');
            } else if (data.is_paused) {
                statusElement.classList.add('status-paused');
            } else if (data.status === '已停止') {
                statusElement.classList.add('status-stopped');
            } else {
                statusElement.classList.add('status-idle');
            }
            
            // 更新进度条
            progressBar.style.width = `${data.progress}%`;
            progressText.textContent = `${data.progress}%`;
            
            // 更新按钮状态
            startBtn.disabled = data.is_running;
            pauseBtn.disabled = !data.is_running || data.is_paused;
            resumeBtn.disabled = !data.is_running || !data.is_paused;
            stopBtn.disabled = !data.is_running;
            
            // 更新日志
            if (data.logs && data.logs.length > 0) {
                data.logs.forEach(log => {
                    allLogs.push(log);
                    const logDiv = document.createElement('div');

                    // 根据日志级别设置样式
                    let logClass = 'log-entry';
                    if (log.includes('[INFO]')) {
                        logClass += ' log-info';
                    } else if (log.includes('[SUCCESS]')) {
                        logClass += ' log-success';
                    } else if (log.includes('[WARNING]')) {
                        logClass += ' log-warning';
                    } else if (log.includes('[ERROR]')) {
                        logClass += ' log-error';
                    } else if (log.includes('[PROGRESS]')) {
                        logClass += ' log-progress';
                    }

                    logDiv.className = logClass;
                    logDiv.textContent = log;
                    logContainer.appendChild(logDiv);
                });

                // 自动滚动到底部
                if (autoScroll) {
                    logContainer.scrollTop = logContainer.scrollHeight;
                }
            }
        }

        // 获取状态
        async function getStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                updateUI(data);
            } catch (error) {
                console.error('Failed to get status:', error);
            }
        }

        // 开始状态轮询
        function startStatusPolling() {
            statusInterval = setInterval(getStatus, 1000);
        }

        // 停止状态轮询
        function stopStatusPolling() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
        }

        // 清空日志
        function clearLogs() {
            logContainer.innerHTML = '';
            allLogs = [];
            showMessage('日志已清空', 'success');
        }

        // 切换自动滚动
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            autoScrollBtn.textContent = `自动滚动: ${autoScroll ? '开' : '关'}`;
            if (autoScroll) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }

        // 导出日志
        function exportLogs() {
            if (allLogs.length === 0) {
                showMessage('没有日志可导出', 'error');
                return;
            }

            const logText = allLogs.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showMessage('日志已导出', 'success');
        }

        // 加载调试配置
        function loadDebugConfig() {
            fetch('/api/debug/config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        debugModeCheckbox.checked = data.debug_enabled;
                        saveIntermediateCheckbox.checked = data.save_intermediate_enabled;
                        saveOriginalCheckbox.checked = config.save_original_screenshot || false;
                        saveCroppedCheckbox.checked = config.save_cropped_image || false;
                        saveDetectionCheckbox.checked = config.save_detection_result || false;
                    }
                })
                .catch(error => {
                    console.error('加载调试配置失败:', error);
                });
        }

        // 应用调试配置
        function applyDebugConfig() {
            const config = {
                enable_debug_mode: debugModeCheckbox.checked,
                save_intermediate_images: saveIntermediateCheckbox.checked,
                save_original_screenshot: saveOriginalCheckbox.checked,
                save_cropped_image: saveCroppedCheckbox.checked,
                save_detection_result: saveDetectionCheckbox.checked
            };

            fetch('/api/debug/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('调试配置已更新', 'success');
                } else {
                    showMessage('更新配置失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('更新配置时发生错误', 'error');
                console.error('Error:', error);
            });
        }

        // 清理调试文件
        function cleanupDebugFiles() {
            if (!confirm('确定要清理调试文件吗？这将删除旧的调试图像文件。')) {
                return;
            }

            fetch('/api/debug/cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ keep_latest: 10 })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('清理失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('清理时发生错误', 'error');
                console.error('Error:', error);
            });
        }

        // 事件监听器
        startBtn.addEventListener('click', () => sendRequest('/api/start'));
        pauseBtn.addEventListener('click', () => sendRequest('/api/pause'));
        resumeBtn.addEventListener('click', () => sendRequest('/api/resume'));
        stopBtn.addEventListener('click', () => sendRequest('/api/stop'));
        clearLogsBtn.addEventListener('click', clearLogs);
        autoScrollBtn.addEventListener('click', toggleAutoScroll);
        exportLogsBtn.addEventListener('click', exportLogs);

        // 调试配置事件监听器
        applyDebugConfigBtn.addEventListener('click', applyDebugConfig);
        cleanupDebugBtn.addEventListener('click', cleanupDebugFiles);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 获取初始状态
            getStatus();

            // 加载调试配置
            loadDebugConfig();

            // 开始状态轮询
            startStatusPolling();
        });

        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', stopStatusPolling);
    </script>
</body>
</html>