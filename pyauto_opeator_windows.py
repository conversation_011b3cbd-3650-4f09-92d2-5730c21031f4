# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv

def main(args):
    pass


import pyautogui
import pygetwindow as gw
import win32gui
import time

# --- 前置步骤：获取句柄 ---
try:
    # 启动一个记事本用于演示
    import subprocess
    subprocess.Popen('notepad.exe')
    time.sleep(1) # 等待窗口打开
    handle = win32gui.FindWindow("Notepad", "无标题 - 记事本")
    if handle == 0:
        raise Exception("找不到记事本窗口")
except Exception as e:
    print(e)
    exit()

# --- 核心步骤：使用 pygetwindow ---
try:
    # 1. 通过句柄获取窗口对象
    target_window = gw.Win32Window(handle)

    # 2. 激活窗口 (非常重要！确保窗口在最前端)
    if not target_window.isActive:
        target_window.activate()
        time.sleep(0.5) # 等待窗口激活

    # 检查窗口是否被最小化，如果是，则恢复
    if target_window.isMinimized:
        target_window.restore()

    print(f"窗口标题: {target_window.title}")
    print(f"窗口位置和大小: Left={target_window.left}, Top={target_window.top}, Width={target_window.width}, Height={target_window.height}")

    # --- 第四步：在指定窗口上执行 pyautogui 操作 ---

    # 1. 在窗口内输入文字
    # 计算点击位置：窗口左上角 + 偏移量
    click_x = target_window.left + 50
    click_y = target_window.top + 50
    pyautogui.click(click_x, click_y)
    pyautogui.write("Hello from PyAutoGUI in a specific window!", interval=0.05)
    pyautogui.press('enter')
    pyautogui.write(f"Window handle is {handle}")

    # 2. 对指定窗口进行截图
    # pyautogui 的 screenshot 函数接受一个 region 参数 (left, top, width, height)
    window_region = (target_window.left, target_window.top, target_window.width, target_window.height)
    screenshot = pyautogui.screenshot(region=window_region)
    screenshot.save("specific_window_screenshot.png")
    print("已对指定窗口截图，并保存为 'specific_window_screenshot.png'")
    time.sleep(1)

    # 3. 在指定窗口内进行图像模板识别
    # 假设你有一张名为 'save_icon.png' 的图片
    # 使用 region 参数将搜索范围限制在该窗口内，这会极大提高速度和准确性！
    try:
        # 这里只是一个示例，实际使用时需要先截取一个真实存在的图标
        # button_location = pyautogui.locateOnScreen('save_icon.png', region=window_region, confidence=0.8)
        # if button_location:
        #     button_center = pyautogui.center(button_location)
        #     pyautogui.click(button_center)
        #     print("在指定窗口内找到了图像并点击了它！")
        # else:
        #     print("在指定窗口内未找到指定的图像。")
        print("图像识别示例：请准备一个窗口内的图标图片（如'save_icon.png'）来测试。")

    except pyautogui.PyAutoGUIException as e:
        print(f"图像识别失败: {e}。可能是因为没有找到匹配的图片。")

finally:
    # 示例结束，关闭窗口
    target_window.close()
    print("操作完成，已关闭窗口。")

except Exception as e:
    print(f"发生错误: {e}")