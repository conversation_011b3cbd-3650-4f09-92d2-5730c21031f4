"""
同花顺窗口截图和模板匹配功能演示

这个文件演示如何使用新添加的截图和模板匹配功能
"""

from open_ths_pyauto import *
import cv2
import os

def simple_screenshot_demo():
    """
    简单的截图演示
    """
    print("=== 简单截图演示 ===")
    
    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        print("无法启动同花顺")
        return
    
    # 连接窗口
    main_window = connect_to_window(process)
    if not main_window:
        print("无法连接到窗口")
        return
    
    # 截图
    screenshot_path = "demo_screenshot.png"
    screenshot = capture_window_screenshot(main_window, screenshot_path)
    
    if screenshot is not None:
        print(f"截图成功！")
        print(f"图像尺寸: {screenshot.shape}")
        print(f"截图保存在: {screenshot_path}")
        
        # 显示截图信息
        height, width, channels = screenshot.shape
        print(f"宽度: {width}px, 高度: {height}px, 通道数: {channels}")
    else:
        print("截图失败")

def template_matching_demo():
    """
    模板匹配演示
    """
    print("=== 模板匹配演示 ===")
    
    # 启动同花顺
    process = launch_tonghuashun_process()
    if not process:
        return
    
    main_window = connect_to_window(process)
    if not main_window:
        return
    
    # 截图
    screenshot = capture_window_screenshot(main_window, "current_screenshot.png")
    if screenshot is None:
        return
    
    # 检查是否有模板文件
    template_files = [
        "search_box_template.png",
        "button_template.png",
        "logo_template.png"
    ]
    
    print("检查模板文件...")
    for template_file in template_files:
        if os.path.exists(template_file):
            print(f"找到模板文件: {template_file}")
            
            # 进行模板匹配
            result = find_template_in_screenshot(screenshot, template_file, threshold=0.7)
            
            if result['found']:
                print(f"  ✓ 匹配成功! 置信度: {result['confidence']:.3f}")
                print(f"  位置: {result['location']}")
                print(f"  中心: {result['center']}")
                print(f"  矩形: {result['rectangle']}")
            else:
                print(f"  ✗ 未找到匹配 (最高置信度: {result['confidence']:.3f})")
        else:
            print(f"模板文件不存在: {template_file}")

def create_template_guide():
    """
    创建模板的指导
    """
    print("=== 如何创建模板图像 ===")
    print()
    print("步骤1: 运行截图功能")
    print("  - 运行 simple_screenshot_demo() 获取完整窗口截图")
    print()
    print("步骤2: 编辑截图")
    print("  - 用图像编辑软件打开截图文件")
    print("  - 裁剪出你想要匹配的区域")
    print("  - 建议模板大小在 50x50 到 200x200 像素之间")
    print()
    print("步骤3: 保存模板")
    print("  - 保存为 PNG 格式")
    print("  - 使用描述性的文件名，如:")
    print("    * search_box_template.png (搜索框)")
    print("    * buy_button_template.png (买入按钮)")
    print("    * sell_button_template.png (卖出按钮)")
    print()
    print("步骤4: 测试模板")
    print("  - 运行 template_matching_demo() 测试匹配效果")
    print("  - 如果匹配不好，可以调整阈值或重新制作模板")
    print()
    print("提示:")
    print("  - 选择独特的、不容易变化的界面元素")
    print("  - 避免包含文字内容（文字可能会变化）")
    print("  - 模板不要太小（容易误匹配）也不要太大（降低匹配精度）")

def pyautogui_template_demo():
    """
    PyAutoGUI模板匹配演示
    """
    print("=== PyAutoGUI模板匹配演示 ===")

    # 检查pyautogui是否可用
    if not check_pyautogui_dependencies():
        return

    print("PyAutoGUI模板匹配功能:")
    print("- 直接在屏幕上查找图像")
    print("- 不需要先截图")
    print("- 支持置信度设置")
    print("- 支持区域搜索")

    while True:
        print("\n请选择操作:")
        print("1. 全屏查找模板")
        print("2. 全屏查找并点击模板")
        print("3. 等待模板出现")
        print("4. 在窗口区域查找模板")
        print("5. 退出")

        choice = input("请输入选择 (1-5): ").strip()

        if choice == "1":
            template_path = input("请输入模板文件路径: ").strip()
            if os.path.exists(template_path):
                confidence = float(input("请输入置信度 (0.1-1.0, 默认0.8): ").strip() or "0.8")
                result = locate_template_on_screen(template_path, confidence)
                if result['found']:
                    print(f"找到模板! 位置: {result['center']}")
                else:
                    print("未找到模板")
            else:
                print("模板文件不存在")

        elif choice == "2":
            template_path = input("请输入模板文件路径: ").strip()
            if os.path.exists(template_path):
                confidence = float(input("请输入置信度 (0.1-1.0, 默认0.8): ").strip() or "0.8")
                success = click_template_on_screen(template_path, confidence)
                if success:
                    print("点击成功!")
                else:
                    print("点击失败")
            else:
                print("模板文件不存在")

        elif choice == "3":
            template_path = input("请输入模板文件路径: ").strip()
            if os.path.exists(template_path):
                timeout = float(input("请输入超时时间 (秒, 默认10): ").strip() or "10")
                confidence = float(input("请输入置信度 (0.1-1.0, 默认0.8): ").strip() or "0.8")
                result = wait_for_template_on_screen(template_path, timeout, confidence)
                if result['found']:
                    print(f"模板出现! 位置: {result['center']}")
                else:
                    print("等待超时")
            else:
                print("模板文件不存在")

        elif choice == "4":
            # 启动同花顺并在窗口区域查找
            process = launch_tonghuashun_process()
            if not process:
                continue

            main_window = connect_to_window(process)
            if not main_window:
                continue

            template_path = input("请输入模板文件路径: ").strip()
            if os.path.exists(template_path):
                confidence = float(input("请输入置信度 (0.1-1.0, 默认0.8): ").strip() or "0.8")
                result = locate_template_in_window(main_window, template_path, confidence)
                if result['found']:
                    print(f"在窗口中找到模板! 位置: {result['center']}")
                else:
                    print("在窗口中未找到模板")
            else:
                print("模板文件不存在")

        elif choice == "5":
            print("退出PyAutoGUI演示")
            break

        else:
            print("无效选择，请重新输入")

def compare_methods_demo():
    """
    比较不同模板匹配方法的演示
    """
    print("=== 模板匹配方法比较演示 ===")

    process = launch_tonghuashun_process()
    if not process:
        return

    main_window = connect_to_window(process)
    if not main_window:
        return

    template_path = input("请输入要测试的模板文件路径: ").strip()
    if not os.path.exists(template_path):
        print("模板文件不存在")
        return

    confidence = 0.8

    print(f"\n使用模板: {template_path}")
    print(f"置信度: {confidence}")
    print("=" * 50)

    # 方法1: OpenCV + 窗口截图
    print("方法1: OpenCV + 窗口截图")
    start_time = time.time()
    screenshot = capture_window_screenshot(main_window)
    if screenshot is not None:
        result1 = find_template_in_screenshot(screenshot, template_path, confidence)
        time1 = time.time() - start_time
        print(f"  结果: {'找到' if result1['found'] else '未找到'}")
        if result1['found']:
            print(f"  置信度: {result1['confidence']:.3f}")
            print(f"  位置: {result1['center']}")
        print(f"  耗时: {time1:.3f}秒")
    else:
        print("  截图失败")

    print()

    # 方法2: PyAutoGUI 全屏搜索
    if check_pyautogui_dependencies():
        print("方法2: PyAutoGUI 全屏搜索")
        start_time = time.time()
        result2 = locate_template_on_screen(template_path, confidence)
        time2 = time.time() - start_time
        print(f"  结果: {'找到' if result2['found'] else '未找到'}")
        if result2['found']:
            print(f"  位置: {result2['center']}")
        print(f"  耗时: {time2:.3f}秒")

        print()

        # 方法3: PyAutoGUI 窗口区域搜索
        print("方法3: PyAutoGUI 窗口区域搜索")
        start_time = time.time()
        result3 = locate_template_in_window(main_window, template_path, confidence)
        time3 = time.time() - start_time
        print(f"  结果: {'找到' if result3['found'] else '未找到'}")
        if result3['found']:
            print(f"  位置: {result3['center']}")
        print(f"  耗时: {time3:.3f}秒")

    print("\n" + "=" * 50)
    print("方法比较总结:")
    print("- OpenCV方法: 精确度高，支持详细的置信度")
    print("- PyAutoGUI全屏: 简单易用，但搜索范围大")
    print("- PyAutoGUI窗口: 结合了两者优点，推荐使用")

def interactive_template_click_demo():
    """
    交互式模板点击演示
    """
    print("=== 交互式模板点击演示 ===")

    process = launch_tonghuashun_process()
    if not process:
        return

    main_window = connect_to_window(process)
    if not main_window:
        return

    while True:
        print("\n请选择操作:")
        print("1. 截图")
        print("2. 查找模板 (OpenCV)")
        print("3. 点击模板 (OpenCV)")
        print("4. 查找模板 (PyAutoGUI)")
        print("5. 点击模板 (PyAutoGUI)")
        print("6. 退出")

        choice = input("请输入选择 (1-6): ").strip()

        if choice == "1":
            screenshot = capture_window_screenshot(main_window, "interactive_screenshot.png")
            if screenshot is not None:
                print("截图完成: interactive_screenshot.png")

        elif choice == "2":
            template_path = input("请输入模板文件路径: ").strip()
            if os.path.exists(template_path):
                screenshot = capture_window_screenshot(main_window)
                if screenshot is not None:
                    result = find_template_in_screenshot(screenshot, template_path)
                    if result['found']:
                        print(f"找到模板! 置信度: {result['confidence']:.3f}")
                        print(f"位置: {result['center']}")
                    else:
                        print("未找到模板")
            else:
                print("模板文件不存在")

        elif choice == "3":
            template_path = input("请输入要点击的模板文件路径: ").strip()
            if os.path.exists(template_path):
                success = click_template_if_found(main_window, template_path)
                if success:
                    print("点击成功!")
                else:
                    print("点击失败")
            else:
                print("模板文件不存在")

        elif choice == "4":
            template_path = input("请输入模板文件路径: ").strip()
            if os.path.exists(template_path):
                confidence = float(input("请输入置信度 (0.1-1.0, 默认0.8): ").strip() or "0.8")
                result = locate_template_in_window(main_window, template_path, confidence)
                if result['found']:
                    print(f"找到模板! 位置: {result['center']}")
                else:
                    print("未找到模板")
            else:
                print("模板文件不存在")

        elif choice == "5":
            template_path = input("请输入要点击的模板文件路径: ").strip()
            if os.path.exists(template_path):
                confidence = float(input("请输入置信度 (0.1-1.0, 默认0.8): ").strip() or "0.8")
                success = click_template_in_window(main_window, template_path, confidence)
                if success:
                    print("点击成功!")
                else:
                    print("点击失败")
            else:
                print("模板文件不存在")

        elif choice == "6":
            print("退出演示")
            break

        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    print("同花顺截图和模板匹配功能演示")
    print("=" * 40)

    while True:
        print("\n请选择演示:")
        print("1. 简单截图演示")
        print("2. 模板匹配演示 (OpenCV)")
        print("3. 模板创建指导")
        print("4. PyAutoGUI模板匹配演示")
        print("5. 方法比较演示")
        print("6. 交互式演示")
        print("7. 退出")

        choice = input("请输入选择 (1-7): ").strip()

        if choice == "1":
            simple_screenshot_demo()
        elif choice == "2":
            template_matching_demo()
        elif choice == "3":
            create_template_guide()
        elif choice == "4":
            pyautogui_template_demo()
        elif choice == "5":
            compare_methods_demo()
        elif choice == "6":
            interactive_template_click_demo()
        elif choice == "7":
            print("再见!")
            break
        else:
            print("无效选择，请重新输入")
