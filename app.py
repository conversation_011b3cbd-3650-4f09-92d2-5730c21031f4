# app.py - Flask应用主文件
from flask import Flask, render_template, request, jsonify
import threading
import time
import queue
from datetime import datetime
import os
import task_main
from logger_manager import get_logger, LogCaptureContext
from debug_config_manager import get_debug_config_manager

app = Flask(__name__)

class TaskManager:
    def __init__(self):
        self.is_running = False
        self.is_paused = False
        self.should_stop = False
        self.current_task = None
        self.task_thread = None
        self.progress = 0
        self.status = "待机"
        self.log_messages = queue.Queue()
        self.start_time = None
        self.logger = get_logger()
        # 设置日志回调，将日志消息添加到队列
        self.logger.set_log_callback(self._on_log_message)

    def _on_log_message(self, message):
        """日志消息回调函数"""
        self.log_messages.put(message)

    def log(self, message):
        """记录日志消息"""
        self.logger.log(message, "INFO")
    
    class WebTaskControl:
        """提供给 task_main 的控制与反馈接口"""
        def __init__(self, manager: "TaskManager"):
            self._m = manager

        @property
        def should_stop(self) -> bool:
            return self._m.should_stop

        @property
        def is_paused(self) -> bool:
            return self._m.is_paused

        def log(self, message: str):
            self._m.log(message)

        def set_status(self, status: str):
            self._m.status = status

        def set_progress(self, progress: int):
            self._m.progress = max(0, min(100, int(progress)))

    def run_task(self, simulate: bool = False):
        """在独立线程中运行 task_main，并支持暂停/停止/进度回传
        simulate=True 时，调用一个安全的模拟模式，不依赖外部应用。"""
        try:
            # 开始日志捕获
            self.logger.start_capture()

            self.log("开始执行任务...")
            self.start_time = time.time()
            self.status = "运行中..."
            control = TaskManager.WebTaskControl(self)
            # 运行用户主任务
            if simulate:
                self.log("以模拟模式运行（不启动同花顺）")
                # 简单模拟：总进度100步，每步检查暂停/停止
                total = 50
                for i in range(total):
                    if self.should_stop:
                        self.log("检测到停止信号，退出模拟任务")
                        break
                    while self.is_paused and not self.should_stop:
                        self.status = "已暂停"; time.sleep(0.1)
                    self.status = f"模拟处理中... ({i+1}/{total})"
                    self.progress = int((i+1)/total*100)
                    time.sleep(0.1)
                else:
                    self.progress = 100
            else:
                task_main.main(args=[], control=control)

            if not self.should_stop and self.progress >= 100:
                elapsed = time.time() - self.start_time if self.start_time else 0
                self.log(f"任务完成！总用时: {elapsed:.2f}秒")
                self.status = "完成"
                self.progress = 100
            elif self.should_stop:
                self.status = "已停止"
        except Exception as e:
            self.log(f"任务发生异常: {e}")
            self.status = "异常"
        finally:
            # 停止日志捕获
            self.logger.stop_capture()
            # 重置状态
            self.is_running = False
            self.current_task = None
            self.task_thread = None

    def start_task(self, simulate: bool = False):
        if self.is_running:
            return False, "任务已在运行中"

        self.is_running = True
        self.is_paused = False
        self.should_stop = False
        self.progress = 0
        self.status = "启动中..."

        # 清空旧的日志
        while not self.log_messages.empty():
            self.log_messages.get()

        # 启动新线程执行任务
        self.task_thread = threading.Thread(target=self.run_task, kwargs={"simulate": simulate})
        self.task_thread.daemon = True
        self.task_thread.start()

        return True, "任务启动成功"

    def pause_task(self):
        if not self.is_running:
            return False, "没有正在运行的任务"
        
        if self.is_paused:
            return False, "任务已经暂停"
        
        self.is_paused = True
        self.log("任务已暂停")
        return True, "任务暂停成功"
    
    def resume_task(self):
        if not self.is_running:
            return False, "没有正在运行的任务"
        
        if not self.is_paused:
            return False, "任务没有暂停"
        
        self.is_paused = False
        self.log("任务已恢复")
        return True, "任务恢复成功"
    
    def stop_task(self):
        if not self.is_running:
            return False, "没有正在运行的任务"
        
        self.should_stop = True
        self.log("正在停止任务...")
        
        # 等待线程结束
        if self.task_thread and self.task_thread.is_alive():
            self.task_thread.join(timeout=2)
        
        self.is_running = False
        self.is_paused = False
        self.progress = 0
        return True, "任务停止成功"
    
    def get_status(self):
        # 获取所有日志消息
        logs = []
        while not self.log_messages.empty():
            try:
                logs.append(self.log_messages.get_nowait())
            except queue.Empty:
                break

        # 同时从logger获取新的日志
        new_logs = self.logger.get_logs()
        logs.extend(new_logs)

        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'status': self.status,
            'progress': self.progress,
            'logs': logs
        }

# 全局任务管理器实例
task_manager = TaskManager()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/start', methods=['POST'])
def start_task_endpoint():
    # 支持通过查询参数 ?simulate=1 来以模拟模式启动
    simulate = request.args.get('simulate', '0') in ('1', 'true', 'True')
    success, message = task_manager.start_task(simulate=simulate)
    return jsonify({'success': success, 'message': message})

@app.route('/api/pause', methods=['POST'])
def pause_task():
    success, message = task_manager.pause_task()
    return jsonify({'success': success, 'message': message})

@app.route('/api/resume', methods=['POST'])
def resume_task():
    success, message = task_manager.resume_task()
    return jsonify({'success': success, 'message': message})

@app.route('/api/stop', methods=['POST'])
def stop_task():
    success, message = task_manager.stop_task()
    return jsonify({'success': success, 'message': message})

@app.route('/api/status')
def get_status():
    return jsonify(task_manager.get_status())

@app.route('/api/debug/config', methods=['GET'])
def get_debug_config():
    """获取调试配置"""
    try:
        debug_manager = get_debug_config_manager()
        config = debug_manager.get_debug_config()
        return jsonify({
            'success': True,
            'config': config,
            'debug_enabled': debug_manager.is_debug_enabled(),
            'save_intermediate_enabled': debug_manager.is_save_intermediate_enabled()
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取配置失败: {str(e)}'})

@app.route('/api/debug/config', methods=['POST'])
def update_debug_config():
    """更新调试配置"""
    try:
        data = request.get_json()
        debug_manager = get_debug_config_manager()

        # 更新配置
        if 'enable_debug_mode' in data:
            debug_manager.enable_debug_mode(data['enable_debug_mode'])

        if 'save_intermediate_images' in data:
            debug_manager.enable_save_intermediate(data['save_intermediate_images'])

        if 'debug_output_dir' in data:
            debug_manager.set_debug_output_dir(data['debug_output_dir'])

        # 更新其他配置项
        config_updates = {}
        for key in ['save_original_screenshot', 'save_cropped_image', 'save_detection_result',
                   'image_format', 'add_timestamp_to_filename']:
            if key in data:
                config_updates[key] = data[key]

        if config_updates:
            debug_manager.update_debug_config(**config_updates)

        # 保存配置
        debug_manager.save_config()

        return jsonify({'success': True, 'message': '配置已更新'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新配置失败: {str(e)}'})

@app.route('/api/debug/cleanup', methods=['POST'])
def cleanup_debug_files():
    """清理调试文件"""
    try:
        data = request.get_json()
        keep_latest = data.get('keep_latest', 10)

        debug_manager = get_debug_config_manager()
        debug_manager.cleanup_debug_directory(keep_latest)

        return jsonify({'success': True, 'message': f'调试文件已清理，保留最新 {keep_latest} 个文件'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'清理失败: {str(e)}'})

if __name__ == '__main__':
    print("启动Flask服务器...")
    print("访问 http://localhost:5000 来控制Python程序")
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)