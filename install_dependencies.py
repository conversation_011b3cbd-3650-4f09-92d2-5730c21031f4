"""
自动安装截图和模板匹配功能所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """
    安装指定的Python包
    
    Args:
        package_name (str): 包名
    
    Returns:
        bool: 安装是否成功
    """
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✓ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败:")
        print(f"  错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ {package_name} 安装时发生未知错误: {str(e)}")
        return False

def check_package(package_name, import_name=None):
    """
    检查包是否已安装
    
    Args:
        package_name (str): 包名
        import_name (str): 导入名（如果与包名不同）
    
    Returns:
        bool: 包是否已安装
    """
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装")
        return False

def main():
    """
    主安装函数
    """
    print("同花顺截图和模板匹配功能 - 依赖包安装程序")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("Pillow", "PIL"),
        ("pywin32", "win32gui"),
    ]
    
    # 检查当前状态
    print("\n1. 检查当前安装状态:")
    missing_packages = []
    
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n✓ 所有依赖包都已安装!")
        print("你可以直接使用截图和模板匹配功能了")
        return
    
    # 安装缺失的包
    print(f"\n2. 需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    print("\n3. 开始安装...")
    
    success_count = 0
    for package in missing_packages:
        if install_package(package):
            success_count += 1
        print()  # 空行分隔
    
    # 安装结果
    print("=" * 50)
    print("安装结果:")
    print(f"成功安装: {success_count}/{len(missing_packages)} 个包")
    
    if success_count == len(missing_packages):
        print("✓ 所有依赖包安装成功!")
        print("\n你现在可以使用以下功能:")
        print("- 窗口截图")
        print("- 图像模板匹配")
        print("- 自动点击模板位置")
        
        print("\n建议运行测试程序验证功能:")
        print("python test_screenshot_features.py")
        
    else:
        print("✗ 部分包安装失败")
        print("\n请手动安装失败的包:")
        for package in missing_packages:
            print(f"pip install {package}")
    
    print("\n安装程序结束")

def install_from_requirements():
    """
    从requirements.txt安装所有依赖
    """
    print("从 requirements.txt 安装依赖...")
    
    if not os.path.exists("requirements.txt"):
        print("✗ requirements.txt 文件不存在")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True, check=True)
        
        print("✓ 从 requirements.txt 安装成功")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print("✗ 从 requirements.txt 安装失败:")
        print(f"错误信息: {e.stderr}")
        return False

def upgrade_packages():
    """
    升级已安装的包到最新版本
    """
    print("升级依赖包到最新版本...")
    
    packages = ["opencv-python", "numpy", "Pillow", "pywin32"]
    
    for package in packages:
        try:
            print(f"升级 {package}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", package
            ], check=True)
            print(f"✓ {package} 升级完成")
        except subprocess.CalledProcessError:
            print(f"✗ {package} 升级失败")

if __name__ == "__main__":
    while True:
        print("\n请选择操作:")
        print("1. 检查并安装缺失的依赖包")
        print("2. 从 requirements.txt 安装所有依赖")
        print("3. 升级依赖包到最新版本")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            main()
        elif choice == "2":
            install_from_requirements()
        elif choice == "3":
            upgrade_packages()
        elif choice == "4":
            print("退出安装程序")
            break
        else:
            print("无效选择，请重新输入")
