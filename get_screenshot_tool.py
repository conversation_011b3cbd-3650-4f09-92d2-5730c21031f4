import cv2
import numpy as np
import os


class ImageCoordinateTool:
    def __init__(self):
        self.image = None
        self.original_image = None
        self.coordinates = []
        self.window_name = "图像坐标获取工具 - 点击获取坐标，按ESC退出，按R重置，按S保存坐标"
        self.scale_factor = 1.0

    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 将显示坐标转换为原始图像坐标
            original_x = int(x / self.scale_factor)
            original_y = int(y / self.scale_factor)

            self.coordinates.append((original_x, original_y))
            print(f"坐标 {len(self.coordinates)}: ({original_x}, {original_y})")

            # 在图像上绘制标记点
            cv2.circle(self.image, (x, y), 5, (0, 0, 255), -1)  # 红色实心圆
            cv2.putText(self.image, f"{len(self.coordinates)}", (x + 10, y - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

            cv2.imshow(self.window_name, self.image)

    def resize_image_if_needed(self, image, max_width=1200, max_height=800):
        """如果图像太大，则调整大小以适应屏幕"""
        height, width = image.shape[:2]

        if width <= max_width and height <= max_height:
            return image, 1.0

        # 计算缩放比例
        width_scale = max_width / width
        height_scale = max_height / height
        scale = min(width_scale, height_scale)

        # 调整图像大小
        new_width = int(width * scale)
        new_height = int(height * scale)
        resized_image = cv2.resize(image, (new_width, new_height))

        return resized_image, scale

    def load_image(self, image_path):
        """加载图像"""
        if not os.path.exists(image_path):
            print(f"错误: 图像文件 '{image_path}' 不存在")
            return False

        self.original_image = cv2.imread(image_path)
        if self.original_image is None:
            print(f"错误: 无法加载图像 '{image_path}'")
            return False

        # 调整图像大小以适应屏幕
        self.image, self.scale_factor = self.resize_image_if_needed(self.original_image)

        print(f"图像加载成功: {image_path}")
        if self.scale_factor != 1.0:
            print(f"图像已缩放，缩放比例: {self.scale_factor:.2f}")
        print(f"原始图像尺寸: {self.original_image.shape[1]}x{self.original_image.shape[0]}")
        print(f"显示图像尺寸: {self.image.shape[1]}x{self.image.shape[0]}")

        return True

    def reset_coordinates(self):
        """重置所有坐标标记"""
        self.coordinates.clear()
        self.image, _ = self.resize_image_if_needed(self.original_image)
        cv2.imshow(self.window_name, self.image)
        print("已重置所有坐标标记")

    def save_coordinates(self, filename="coordinates.txt"):
        """保存坐标到文件"""
        if not self.coordinates:
            print("没有坐标可保存")
            return

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("图像坐标点记录\n")
                f.write("=" * 30 + "\n")
                for i, (x, y) in enumerate(self.coordinates, 1):
                    f.write(f"点 {i}: ({x}, {y})\n")

                # 也保存为Python列表格式，方便直接使用
                f.write("\nPython列表格式:\n")
                f.write(f"coordinates = {self.coordinates}\n")

            print(f"坐标已保存到文件: {filename}")
        except Exception as e:
            print(f"保存文件时出错: {e}")

    def twice_run(self, image_path):
        coor_list = []
        """运行坐标获取工具"""
        if not self.load_image(image_path):
            return

        # 创建窗口并设置鼠标回调
        cv2.namedWindow(self.window_name, cv2.WINDOW_AUTOSIZE)
        cv2.setMouseCallback(self.window_name, self.mouse_callback)

        # 显示图像
        cv2.imshow(self.window_name, self.image)

        print("\n使用说明:")
        print("- 鼠标左键点击：获取坐标")
        print("- ESC键：退出程序")
        print("- R键：重置所有标记")
        print("- S键：保存坐标到文件")
        print("- C键：清空终端显示的坐标")
        print("\n开始点击图像获取坐标...")

        while True:
            key = cv2.waitKey(1) & 0xFF

            if key == 27:  # ESC键退出
                break
            elif key == ord('r') or key == ord('R'):  # R键重置
                self.reset_coordinates()
            elif key == ord('s') or key == ord('S'):  # S键保存
                self.save_coordinates()
            elif key == ord('c') or key == ord('C'):  # C键清空显示
                os.system('cls' if os.name == 'nt' else 'clear')
                print("已清空终端显示")
                print("当前坐标列表:")
                for i, (x, y) in enumerate(self.coordinates, 1):
                    print(f"点 {i}: ({x}, {y})")

        cv2.destroyAllWindows()

        # 程序结束时显示所有坐标
        if self.coordinates:
            print(f"\n总共获取了 {len(self.coordinates)} 个坐标点:")
            for i, (x, y) in enumerate(self.coordinates, 1):
                print(f"点 {i}: ({x}, {y})")
                coor_list.append([x, y])

            print(f"\nPython列表格式:")
            print(f"coordinates = {self.coordinates}")
        else:
            print("\n没有获取任何坐标点")
        return coor_list
    
    def run(self, image_path):
        coor_list = []
        """运行坐标获取工具"""
        if not self.load_image(image_path):
            return

        # 创建窗口并设置鼠标回调
        cv2.namedWindow(self.window_name, cv2.WINDOW_AUTOSIZE)
        cv2.setMouseCallback(self.window_name, self.mouse_callback)

        # 显示图像
        cv2.imshow(self.window_name, self.image)

        print("\n使用说明:")
        print("- 鼠标左键点击：获取坐标")
        print("- ESC键：退出程序")
        print("- R键：重置所有标记")
        print("- S键：保存坐标到文件")
        print("- C键：清空终端显示的坐标")
        print("\n开始点击图像获取坐标...")

        while True:
            key = cv2.waitKey(1) & 0xFF

            if key == 27:  # ESC键退出
                break
            elif key == ord('r') or key == ord('R'):  # R键重置
                self.reset_coordinates()
            elif key == ord('s') or key == ord('S'):  # S键保存
                self.save_coordinates()
            elif key == ord('c') or key == ord('C'):  # C键清空显示
                os.system('cls' if os.name == 'nt' else 'clear')
                print("已清空终端显示")
                print("当前坐标列表:")
                for i, (x, y) in enumerate(self.coordinates, 1):
                    print(f"点 {i}: ({x}, {y})")

        cv2.destroyAllWindows()

        # 程序结束时显示所有坐标
        if self.coordinates:
            print(f"\n总共获取了 {len(self.coordinates)} 个坐标点:")
            for i, (x, y) in enumerate(self.coordinates, 1):
                print(f"点 {i}: ({x}, {y})")
                coor_list.append([x, y])

            print(f"\nPython列表格式:")
            print(f"coordinates = {self.coordinates}")
        else:
            print("\n没有获取任何坐标点")
        return coor_list



def main():
    """主函数"""
    print("OpenCV 图像坐标获取工具")
    print("=" * 30)

    # 获取图像路径
    image_path = input("请输入图像文件路径: ").strip().strip('"\'')

    if not image_path:
        print("错误: 请提供有效的图像路径")
        return

    # 创建工具实例并运行
    tool = ImageCoordinateTool()
    tool.run(image_path)

def get_coor(image_path):
    """主函数"""
    print("正在调用OpenCV 图像坐标获取工具")
    print("=" * 30)

    if not image_path:
        print("错误: 请提供有效的图像路径")
        return

    # 创建工具实例并运行
    tool = ImageCoordinateTool()
    coor_list = tool.run(image_path)
    return coor_list

if __name__ == "__main__":
    main()