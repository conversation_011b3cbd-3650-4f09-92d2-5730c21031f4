# 交互式屏幕截图功能说明

## 功能概述

在 `windows_precessing.py` 中新增了交互式屏幕截图功能，允许用户通过全局快捷键或直接调用的方式进行屏幕区域选择和自动截图，并自动进行K线手动选择。

## 主要特性

### 1. 全局快捷键触发
- **快捷键**: `Ctrl+Shift+S`
- **功能**: 在任何时候按下快捷键都可以启动交互式截图功能
- **退出**: 按 `Ctrl+C` 退出快捷键监听模式

### 2. 全屏点选功能
- 显示半透明的全屏覆盖层
- 支持鼠标拖拽选择矩形区域
- 实时显示选择框预览
- 显示选择区域的坐标和尺寸信息

### 3. 自动截图
- 根据用户选择的区域自动截取屏幕截图
- 自动生成带时间戳的文件名
- 保存到 `resources/images/` 目录

### 4. K线中心点选择
- 截图完成后自动打开截图进行K线中心点选择
- 直接在截图上点击K线的中心点（推荐选择15个点）
- 自动计算K线宽度和范围，无需手动选择区域

### 5. 配置文件记录
- 自动更新 `resources/coor_config.json` 配置文件
- 记录原始选择坐标、K线选择坐标、截图路径等信息
- 添加时间戳记录

## 使用方法

### 方法一：运行主程序
```bash
python windows_precessing.py
```

程序会显示菜单：
1. 启动全局快捷键监听模式 (Ctrl+Shift+S)
2. 立即执行交互式截图
3. 执行传统的窗口预处理流程
4. 退出

### 方法二：直接测试
```bash
python test_interactive_screenshot.py
```

### 方法三：在代码中调用
```python
from windows_precessing import interactive_screenshot_capture

# 直接执行交互式截图
screenshot_path = interactive_screenshot_capture()
```

## 操作流程

### 1. 启动功能
- 选择运行模式或按下快捷键 `Ctrl+Shift+S`

### 2. 区域选择
- 屏幕会显示半透明黑色覆盖层
- 用鼠标拖拽选择要截图的矩形区域
- 选择过程中会显示红色选择框
- 完成选择后会显示确认信息

### 3. 确认选择
- 按 `回车键` 确认选择
- 按 `ESC键` 取消选择

### 4. 自动截图
- 程序自动截取选择的区域
- 截图保存到 `resources/images/` 目录

### 5. K线中心点选择
- 自动打开截图进行K线中心点选择
- 在弹出的窗口中点击K线的中心点（建议选择15个点）
- 完成选择后按任意键或等待自动完成
- 按 `ESC键` 可以取消选择

### 6. 自动处理
- 程序自动计算K线宽度和范围
- 直接更新配置文件中的K线坐标信息
- 无需额外的图像裁剪步骤

## 配置文件更新

程序会在 `resources/coor_config.json` 中添加以下信息：

```json
{
    "interactive_capture": {
        "last_screenshot_path": "截图文件路径",
        "original_selection": {
            "p1": [x1, y1],
            "p2": [x2, y2]
        },
        "kline_centers": [x1, x2, x3, ...],
        "kline_count": 15,
        "timestamp": "2025-08-23 22:30:00"
    },
    "line_x": [x1, x2, x3, ...],
    "line_width": 32,
    "lines_range": [[x1-width, x1+width], [x2-width, x2+width], ...]
}
```

## 依赖库

确保安装以下依赖库：

```bash
pip install keyboard
pip install pyautogui
pip install Pillow
pip install opencv-python
pip install numpy
```

## 注意事项

### 1. 权限要求
- 全局快捷键功能可能需要管理员权限
- 如果快捷键无法工作，请以管理员身份运行程序

### 2. 屏幕分辨率
- 功能支持任意屏幕分辨率
- 自动适配多显示器环境

### 3. 文件保存
- 截图文件自动保存到 `resources/images/` 目录
- 文件名格式：`interactive_screenshot_YYYYMMDD_HHMMSS.png`

### 4. 错误处理
- 程序包含完整的错误处理机制
- 如果交互式功能失败，会自动回退到传统模式

## 与现有功能的区别

| 特性 | 传统模式 | 交互式模式 |
|------|----------|------------|
| 图片来源 | 需要手动指定图片路径 | 实时屏幕截图 |
| 区域选择 | 在静态图片上选择 | 在实时屏幕上选择 |
| 用户体验 | 需要多步操作 | 一键完成 |
| 自动化程度 | 半自动 | 全自动 |
| 快捷键支持 | 无 | 支持全局快捷键 |

## 故障排除

### 1. 快捷键不工作
- 检查是否以管理员权限运行
- 确认 keyboard 库已正确安装
- 尝试使用直接调用模式

### 2. 覆盖层不显示
- 检查 tkinter 是否正确安装
- 确认显示器设置正常
- 尝试重启程序

### 3. 截图失败
- 检查 pyautogui 库是否正确安装
- 确认有足够的磁盘空间
- 检查目录权限

### 4. K线选择失败
- 确认 opencv-python 库已安装
- 检查截图文件是否正常生成
- 尝试手动打开截图文件验证

## 更新日志

- **2025-08-23**: 初始版本发布
  - 添加全局快捷键支持
  - 实现全屏区域选择功能
  - 集成自动截图和K线选择
  - 完善配置文件记录功能
