# logger_manager.py - 统一日志管理模块
import sys
import threading
import queue
from datetime import datetime
from typing import Optional, Callable
import functools

class LoggerManager:
    """统一日志管理器，用于捕获和管理所有日志输出"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        
        self.log_queue = queue.Queue()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.is_capturing = False
        self.log_callback = None
        self._lock = threading.Lock()
        
    def set_log_callback(self, callback: Optional[Callable[[str], None]]):
        """设置日志回调函数"""
        with self._lock:
            self.log_callback = callback
    
    def start_capture(self):
        """开始捕获日志"""
        if not self.is_capturing:
            sys.stdout = LogCapture(self, self.original_stdout)
            sys.stderr = LogCapture(self, self.original_stderr)
            self.is_capturing = True
    
    def stop_capture(self):
        """停止捕获日志"""
        if self.is_capturing:
            sys.stdout = self.original_stdout
            sys.stderr = self.original_stderr
            self.is_capturing = False
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # 包含毫秒
        formatted_message = f"[{timestamp}] [{level}] {message}"
        
        # 添加到队列
        self.log_queue.put(formatted_message)
        
        # 调用回调函数
        if self.log_callback:
            try:
                self.log_callback(formatted_message)
            except Exception as e:
                # 避免回调函数异常影响日志记录
                self.original_stdout.write(f"日志回调异常: {e}\n")
        
        # 输出到原始stdout
        self.original_stdout.write(formatted_message + '\n')
        self.original_stdout.flush()
    
    def get_logs(self, max_count: int = 100) -> list:
        """获取日志消息"""
        logs = []
        count = 0
        while not self.log_queue.empty() and count < max_count:
            try:
                logs.append(self.log_queue.get_nowait())
                count += 1
            except queue.Empty:
                break
        return logs
    
    def clear_logs(self):
        """清空日志队列"""
        while not self.log_queue.empty():
            try:
                self.log_queue.get_nowait()
            except queue.Empty:
                break

class LogCapture:
    """日志捕获类"""
    
    def __init__(self, logger_manager: LoggerManager, original_stream):
        self.logger_manager = logger_manager
        self.original_stream = original_stream
        
    def write(self, text):
        # 写入原始流
        self.original_stream.write(text)
        self.original_stream.flush()
        
        # 处理日志消息
        text = text.strip()
        if text and not text.startswith('[') and not text.startswith('INFO:') and not text.startswith('WARNING:'):
            # 避免重复记录已经格式化的日志
            self.logger_manager.log(text)
    
    def flush(self):
        self.original_stream.flush()

def get_logger():
    """获取全局日志管理器实例"""
    return LoggerManager()

def log_decorator(level: str = "INFO"):
    """日志装饰器，用于自动记录函数调用"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger()
            func_name = func.__name__
            logger.log(f"调用函数: {func_name}", level)
            try:
                result = func(*args, **kwargs)
                logger.log(f"函数 {func_name} 执行完成", level)
                return result
            except Exception as e:
                logger.log(f"函数 {func_name} 执行异常: {e}", "ERROR")
                raise
        return wrapper
    return decorator

# 便捷的日志函数
def log_info(message: str):
    """记录信息日志"""
    get_logger().log(message, "INFO")

def log_warning(message: str):
    """记录警告日志"""
    get_logger().log(message, "WARNING")

def log_error(message: str):
    """记录错误日志"""
    get_logger().log(message, "ERROR")

def log_success(message: str):
    """记录成功日志"""
    get_logger().log(message, "SUCCESS")

def log_progress(message: str):
    """记录进度日志"""
    get_logger().log(message, "PROGRESS")

# 替换print函数的便捷函数
def print_to_log(*args, sep=' ', end='\n', level="INFO"):
    """替换print函数，将输出重定向到日志系统"""
    message = sep.join(str(arg) for arg in args) + end.rstrip('\n')
    get_logger().log(message, level)

# 上下文管理器，用于临时捕获日志
class LogCaptureContext:
    """日志捕获上下文管理器"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def __enter__(self):
        self.logger.start_capture()
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.stop_capture()

# 使用示例：
# with LogCaptureContext() as logger:
#     print("这条消息会被捕获到日志系统")
#     logger.log("直接记录日志", "INFO")
