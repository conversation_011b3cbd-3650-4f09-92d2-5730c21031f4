# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块
try:
    import xbot
    from xbot import print, sleep
    from .import package
    from .package import variables as glv
except:
    print("running on your personal PC")
import pandas as pd
import numpy as np
import pymysql
from pymysql.cursors import DictCursor

class DatabaseManager:
    """
    数据库连接上下文管理器 (与上一版代码相同)。
    - 初始化时接收数据库配置。
    - 使用 'with' 语句可以自动连接、提交/回滚和关闭连接。
    """
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = None
        self.cursor = None

    def __enter__(self):
        """当进入 'with' 语句块时调用，建立连接并返回游标。"""
        try:
            self.conn = pymysql.connect(**self.db_config)
            # 使用配置中指定的游标类
            self.cursor = self.conn.cursor()
            return self.cursor
        except pymysql.MySQLError as e:
            print(f"数据库连接失败: {e}")
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """当退出 'with' 语句块时调用。"""
        # 对于只读操作，commit 和 rollback 不是必须的，但保留下来使类更通用
        if self.conn:
            if exc_type:
                self.conn.rollback()
            else:
                self.conn.commit()
        
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        # print("数据库连接已关闭。") # 读取操作可以不用每次都提示关闭
        return False

def fetch_stock_data(db_config, table_name='status_signal'):
    """
    从指定表中读取 'code' 和 'name' 字段。

    Args:
        db_config (dict): 数据库连接配置。
        table_name (str): 要查询的表名。

    Returns:
        list: 包含字典的列表，每个字典代表一行数据。例如: [{'code': '000001', 'name': '平安银行'}, ...]
              如果发生错误或没有数据，则返回空列表。
    """
    results = []
    # 构建 SQL 查询语句
    query = f"SELECT `code`, `name` FROM `{table_name}`;"
    
    try:
        db_manager = DatabaseManager(db_config)
        with db_manager as cursor:
            print(f"正在从 '{table_name}' 表中读取数据...")
            # 执行查询
            cursor.execute(query)
            # 获取所有查询结果
            results = cursor.fetchall()
            print(f"成功读取了 {cursor.rowcount} 条记录。")

    except pymysql.MySQLError as e:
        print(f"读取数据时发生错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")
        
    return results

def fetch_data_as_dataframe(db_config, table_name='status_signal'):
    """
    从指定表中读取 'code' 和 'name' 字段，并返回一个带有正确表头的 Pandas DataFrame。
    """
    query = f"SELECT `code`, `name` FROM `{table_name}`;"
    
    try:
        db_manager = DatabaseManager(db_config)
        with db_manager as cursor:
            print(f"正在从 '{table_name}' 表中读取数据...")
            cursor.execute(query)
            
            # --- 这是本次修改的核心 ---
            # 1. 获取所有查询结果 (这可能是元组列表)
            results_list = cursor.fetchall()
            
            # 2. 从游标的 .description 属性中动态提取列名
            # cursor.description 会返回一个包含列信息的元组，我们只需要每个元组的第一个元素（即列名）
            column_names = [desc[0] for desc in cursor.description]
            
            print(f"成功读取了 {cursor.rowcount} 条记录，列名为: {column_names}")

            # 3. 在创建 DataFrame 时，使用 'columns' 参数显式指定列名
            df = pd.DataFrame(results_list, columns=column_names)
            return df

    except pymysql.MySQLError as e:
        print(f"读取数据时发生错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")
        
    return pd.DataFrame()


def get_stock_dict():
    pd_stock = pd.read_csv(r'C:\Users\<USER>\Documents\ths_gs_resource\stock_pool.csv', dtype={'code': str})
    stock_pool_list = list(pd_stock['code'])
    stock_pool_name_list = list(pd_stock['name'])
    stock_pool_dict= {}
    for i, stock in enumerate(stock_pool_list):
        stock_pool_dict[stock] = {}
        stock_pool_dict[stock]['code'] = stock_pool_list[i]
        stock_pool_dict[stock]['name'] = stock_pool_name_list[i]
        stock_pool_dict[stock]['LD_flag_list'] = np.zeros(14)        
        stock_pool_dict[stock]['LM_flag_list'] = np.zeros(14)
    return stock_pool_dict

def main(args):
    stock_pool_dict = get_stock_dict()
    print(stock_pool_dict)


# --- 使用示例 ---
if __name__ == "__main__":
    # if __name__ == '__main__':
    # get_stock_dict()
    # 1. 你的数据库连接信息
    # !!! 重要：添加了 'cursorclass': DictCursor，这会让查询结果变成字典格式
    db_connection_config = {
        'host': '**************',
        'user': 'ken',
        'password': 'li173312',
        'database': 'ths_stock',
        'charset': 'utf8mb4', # 建议总是指定字符集
        'cursorclass': pymysql.cursors.Cursor # 使用标准游标
    }
    all_stocks_tuple = fetch_stock_data(db_connection_config, table_name='favorite_list')

    # 2. 调用函数获取数据
    all_stocks_df = fetch_data_as_dataframe(db_connection_config, table_name='favorite_list')

    # 3. 处理并打印获取到的数据
    if all_stocks_tuple:
        print("\n--- 从数据库读取到的数据 ---")
        for stock in all_stocks_tuple:
            # 因为使用了 DictCursor，我们可以通过列名来访问数据
            print(f"代码: {stock[0]}, 名称: {stock[1]}")
    else:
        print("\n未能从数据库中获取到任何数据。")
