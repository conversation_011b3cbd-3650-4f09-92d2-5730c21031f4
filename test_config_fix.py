#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件修复验证脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_update_function():
    """测试配置更新函数"""
    print("=" * 60)
    print("配置更新函数测试")
    print("=" * 60)
    
    try:
        # 导入更新函数
        from windows_precessing import update_config_with_kline_centers
        
        # 模拟测试数据
        test_kline_centers = [100, 200, 300, 400, 500]
        test_screenshot_path = "test_screenshot.png"
        test_original_coords = [(50, 50), (600, 400)]
        
        print("测试数据：")
        print(f"K线中心点: {test_kline_centers}")
        print(f"截图路径: {test_screenshot_path}")
        print(f"原始坐标: {test_original_coords}")
        print()
        
        # 备份当前配置
        config_path = "resources/coor_config.json"
        backup_path = "resources/coor_config_backup.json"
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                original_config = json.load(f)
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(original_config, f, ensure_ascii=False, indent=4)
            print("✓ 已备份原始配置文件")
        
        # 执行配置更新
        print("\n正在执行配置更新...")
        update_config_with_kline_centers(test_kline_centers, test_screenshot_path, test_original_coords)
        
        # 验证更新结果
        print("\n验证更新结果：")
        verify_update_result(test_kline_centers, test_original_coords)
        
        # 询问是否恢复原始配置
        restore = input("\n是否恢复原始配置？(y/N): ").strip().lower()
        if restore == 'y' and os.path.exists(backup_path):
            with open(backup_path, 'r', encoding='utf-8') as f:
                original_config = json.load(f)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(original_config, f, ensure_ascii=False, indent=4)
            os.remove(backup_path)
            print("✓ 已恢复原始配置文件")
        elif os.path.exists(backup_path):
            os.remove(backup_path)
            print("✓ 已删除备份文件，保留更新后的配置")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def verify_update_result(expected_kline_centers, expected_original_coords):
    """验证更新结果"""
    try:
        config_path = "resources/coor_config.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证 main_kline_capture_coor
        main_coor = config.get("main_kline_capture_coor", {})
        actual_p1 = main_coor.get("p1")
        actual_p2 = main_coor.get("p2")
        
        if actual_p1 == list(expected_original_coords[0]) and actual_p2 == list(expected_original_coords[1]):
            print("✓ main_kline_capture_coor 更新正确")
            print(f"  p1: {actual_p1}")
            print(f"  p2: {actual_p2}")
        else:
            print("❌ main_kline_capture_coor 更新错误")
            print(f"  期望 p1: {list(expected_original_coords[0])}, 实际: {actual_p1}")
            print(f"  期望 p2: {list(expected_original_coords[1])}, 实际: {actual_p2}")
        
        # 验证 line_x
        actual_line_x = config.get("line_x", [])
        if actual_line_x == expected_kline_centers:
            print("✓ line_x 更新正确")
            print(f"  值: {actual_line_x}")
        else:
            print("❌ line_x 更新错误")
            print(f"  期望: {expected_kline_centers}, 实际: {actual_line_x}")
        
        # 验证 line_width
        expected_width = int(0.85 * (expected_kline_centers[1] - expected_kline_centers[0]) / 2)
        actual_width = config.get("line_width")
        if actual_width == expected_width:
            print("✓ line_width 计算正确")
            print(f"  值: {actual_width}")
        else:
            print("❌ line_width 计算错误")
            print(f"  期望: {expected_width}, 实际: {actual_width}")
        
        # 验证 lines_range
        expected_ranges = []
        for x_coord in expected_kline_centers:
            expected_ranges.append([x_coord - expected_width, x_coord + expected_width])
        
        actual_ranges = config.get("lines_range", [])
        if actual_ranges == expected_ranges:
            print("✓ lines_range 计算正确")
            print(f"  数量: {len(actual_ranges)}")
        else:
            print("❌ lines_range 计算错误")
            print(f"  期望数量: {len(expected_ranges)}, 实际数量: {len(actual_ranges)}")
        
        # 验证 interactive_capture
        interactive = config.get("interactive_capture", {})
        if "kline_centers" in interactive and interactive["kline_centers"] == expected_kline_centers:
            print("✓ interactive_capture.kline_centers 更新正确")
        else:
            print("❌ interactive_capture.kline_centers 更新错误")
        
        if "kline_count" in interactive and interactive["kline_count"] == len(expected_kline_centers):
            print("✓ interactive_capture.kline_count 更新正确")
        else:
            print("❌ interactive_capture.kline_count 更新错误")
        
        if "timestamp" in interactive:
            print("✓ interactive_capture.timestamp 已设置")
            print(f"  时间: {interactive['timestamp']}")
        else:
            print("❌ interactive_capture.timestamp 未设置")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")

def show_current_config_summary():
    """显示当前配置摘要"""
    try:
        config_path = "resources/coor_config.json"
        
        if not os.path.exists(config_path):
            print("❌ 配置文件不存在")
            return
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("当前配置摘要：")
        print("-" * 40)
        
        # main_kline_capture_coor
        main_coor = config.get("main_kline_capture_coor", {})
        print(f"main_kline_capture_coor: {main_coor.get('p1')} -> {main_coor.get('p2')}")
        
        # K线相关
        line_x = config.get("line_x", [])
        print(f"line_x: {len(line_x)} 个点 {line_x[:3]}{'...' if len(line_x) > 3 else ''}")
        print(f"line_width: {config.get('line_width', 'N/A')}")
        
        lines_range = config.get("lines_range", [])
        print(f"lines_range: {len(lines_range)} 个范围")
        
        # 交互式截图信息
        interactive = config.get("interactive_capture", {})
        if interactive:
            print(f"interactive_capture:")
            print(f"  kline_count: {interactive.get('kline_count', 'N/A')}")
            print(f"  timestamp: {interactive.get('timestamp', 'N/A')}")
        else:
            print("interactive_capture: 未设置")
        
    except Exception as e:
        print(f"❌ 读取配置时发生错误: {e}")

def main():
    """主函数"""
    print("配置文件修复验证工具")
    print("=" * 40)
    
    while True:
        print("\n选择操作：")
        print("1. 测试配置更新函数")
        print("2. 查看当前配置摘要")
        print("3. 退出")
        
        try:
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == '1':
                test_config_update_function()
            elif choice == '2':
                show_current_config_summary()
            elif choice == '3':
                print("退出程序")
                break
            else:
                print("无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
