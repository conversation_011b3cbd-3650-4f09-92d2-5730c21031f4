# debug_config_manager.py - 调试配置管理模块
import json
import os
from logger_manager import log_info, log_warning, log_error, log_success

class DebugConfigManager:
    """调试配置管理器"""
    
    def __init__(self, config_path="resources/coor_config.json"):
        self.config_path = config_path
        self.config = {}
        self.debug_config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.debug_config = self.config.get("debug_config", {})
                log_info(f"配置文件加载成功: {self.config_path}")
            else:
                log_error(f"配置文件不存在: {self.config_path}")
                self._create_default_config()
        except Exception as e:
            log_error(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.debug_config = {
            "enable_debug_mode": False,
            "save_intermediate_images": False,
            "debug_output_dir": "resources/cache/debug",
            "save_original_screenshot": True,
            "save_cropped_image": True,
            "save_detection_result": True,
            "image_format": "png",
            "add_timestamp_to_filename": True
        }
        log_warning("使用默认调试配置")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            self.config["debug_config"] = self.debug_config
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            log_success(f"配置已保存到: {self.config_path}")
            return True
        except Exception as e:
            log_error(f"保存配置失败: {e}")
            return False
    
    def is_debug_enabled(self):
        """检查是否启用调试模式"""
        return self.debug_config.get("enable_debug_mode", False)
    
    def is_save_intermediate_enabled(self):
        """检查是否保存中间图像"""
        return self.debug_config.get("save_intermediate_images", False)
    
    def get_debug_output_dir(self):
        """获取调试输出目录"""
        return self.debug_config.get("debug_output_dir", "resources/cache/debug")
    
    def enable_debug_mode(self, enable=True):
        """启用/禁用调试模式"""
        self.debug_config["enable_debug_mode"] = enable
        log_info(f"调试模式已{'启用' if enable else '禁用'}")
    
    def enable_save_intermediate(self, enable=True):
        """启用/禁用保存中间图像"""
        self.debug_config["save_intermediate_images"] = enable
        log_info(f"中间图像保存已{'启用' if enable else '禁用'}")
    
    def set_debug_output_dir(self, directory):
        """设置调试输出目录"""
        self.debug_config["debug_output_dir"] = directory
        log_info(f"调试输出目录设置为: {directory}")
    
    def get_debug_config(self):
        """获取完整的调试配置"""
        return self.debug_config.copy()
    
    def update_debug_config(self, **kwargs):
        """更新调试配置"""
        for key, value in kwargs.items():
            if key in self.debug_config:
                self.debug_config[key] = value
                log_info(f"调试配置更新: {key} = {value}")
            else:
                log_warning(f"未知的调试配置项: {key}")
    
    def print_debug_status(self):
        """打印当前调试状态"""
        print("\n=== 调试配置状态 ===")
        print(f"调试模式: {'启用' if self.is_debug_enabled() else '禁用'}")
        print(f"保存中间图像: {'启用' if self.is_save_intermediate_enabled() else '禁用'}")
        print(f"调试输出目录: {self.get_debug_output_dir()}")
        print(f"保存原始截图: {self.debug_config.get('save_original_screenshot', True)}")
        print(f"保存裁剪图像: {self.debug_config.get('save_cropped_image', True)}")
        print(f"保存检测结果: {self.debug_config.get('save_detection_result', True)}")
        print(f"图像格式: {self.debug_config.get('image_format', 'png')}")
        print(f"文件名添加时间戳: {self.debug_config.get('add_timestamp_to_filename', True)}")
        print("==================")
    
    def create_debug_directory(self):
        """创建调试目录"""
        debug_dir = self.get_debug_output_dir()
        try:
            os.makedirs(debug_dir, exist_ok=True)
            log_success(f"调试目录已创建: {debug_dir}")
            return debug_dir
        except Exception as e:
            log_error(f"创建调试目录失败: {e}")
            return None
    
    def cleanup_debug_directory(self, keep_latest=10):
        """清理调试目录，保留最新的文件"""
        debug_dir = self.get_debug_output_dir()
        if not os.path.exists(debug_dir):
            return
        
        try:
            # 获取所有文件并按修改时间排序
            files = []
            for filename in os.listdir(debug_dir):
                filepath = os.path.join(debug_dir, filename)
                if os.path.isfile(filepath):
                    files.append((filepath, os.path.getmtime(filepath)))
            
            # 按时间排序，最新的在前
            files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除多余的文件
            deleted_count = 0
            for filepath, _ in files[keep_latest:]:
                try:
                    os.remove(filepath)
                    deleted_count += 1
                except Exception as e:
                    log_warning(f"删除文件失败: {filepath}, {e}")
            
            if deleted_count > 0:
                log_info(f"清理调试目录完成，删除了 {deleted_count} 个文件")
            
        except Exception as e:
            log_error(f"清理调试目录失败: {e}")

# 全局配置管理器实例
_debug_config_manager = None

def get_debug_config_manager():
    """获取全局调试配置管理器实例"""
    global _debug_config_manager
    if _debug_config_manager is None:
        _debug_config_manager = DebugConfigManager()
    return _debug_config_manager

def is_debug_mode_enabled():
    """快捷函数：检查是否启用调试模式"""
    return get_debug_config_manager().is_debug_enabled()

def is_save_intermediate_enabled():
    """快捷函数：检查是否保存中间图像"""
    return get_debug_config_manager().is_save_intermediate_enabled()

def get_debug_config():
    """快捷函数：获取调试配置"""
    return get_debug_config_manager().get_debug_config()

def enable_debug_mode(enable=True):
    """快捷函数：启用/禁用调试模式"""
    manager = get_debug_config_manager()
    manager.enable_debug_mode(enable)
    manager.save_config()

def enable_save_intermediate(enable=True):
    """快捷函数：启用/禁用保存中间图像"""
    manager = get_debug_config_manager()
    manager.enable_save_intermediate(enable)
    manager.save_config()

def print_debug_status():
    """快捷函数：打印调试状态"""
    get_debug_config_manager().print_debug_status()

# 使用示例
if __name__ == "__main__":
    # 测试配置管理器
    manager = DebugConfigManager()
    
    print("当前配置:")
    manager.print_debug_status()
    
    print("\n启用调试模式...")
    manager.enable_debug_mode(True)
    manager.enable_save_intermediate(True)
    
    print("更新后的配置:")
    manager.print_debug_status()
    
    # 保存配置
    manager.save_config()
    
    print("\n测试完成")
